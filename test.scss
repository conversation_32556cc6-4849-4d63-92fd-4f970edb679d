// SCSS 测试文件

// 变量测试
$primary-color: #007bff;
$secondary-color: #6c757d;
$font-size-base: 16px;
$spacing-unit: 8px;

// Mixin 测试
@mixin button-style($bg-color, $text-color: white) {
  background-color: $bg-color;
  color: $text-color;
  padding: $spacing-unit * 2;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  
  &:hover {
    opacity: 0.8;
  }
}

// 嵌套测试
.test-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  
  .header {
    background-color: $primary-color;
    padding: $spacing-unit * 3;
    
    h1 {
      color: white;
      font-size: $font-size-base * 2;
      margin: 0;
    }
  }
  
  .content {
    padding: $spacing-unit * 2;
    
    .button {
      @include button-style($primary-color);
      margin-right: $spacing-unit;
    }
    
    .button-secondary {
      @include button-style($secondary-color);
    }
  }
  
  .footer {
    background-color: lighten($secondary-color, 20%);
    padding: $spacing-unit;
    text-align: center;
  }
}

// 响应式测试
@media (max-width: 768px) {
  .test-container {
    .header h1 {
      font-size: $font-size-base * 1.5;
    }
    
    .content {
      padding: $spacing-unit;
      
      .button,
      .button-secondary {
        display: block;
        width: 100%;
        margin: $spacing-unit 0;
      }
    }
  }
}
