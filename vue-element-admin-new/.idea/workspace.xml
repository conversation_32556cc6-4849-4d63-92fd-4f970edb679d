<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1e2d6211-88d6-47d9-a1a2-6fd385302316" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/components.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/HelloWorld.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/HelloWorld.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/Logos.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/Logos.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/MessageBoxDemo.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/MessageBoxDemo.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/layouts/BaseHeader.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/layouts/BaseHeader.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/layouts/BaseSide.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/layouts/BaseSide.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/main.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/nav/1/item-1.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/nav/1/item-1.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/nav/2.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/nav/2.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/nav/4.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/nav/4.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/styles/index.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/styles/index.scss" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;phodal&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/element-plus/element-plus-vite-starter&quot;,
    &quot;accountId&quot;: &quot;082576f8-152b-4189-8d92-d28c37f55ffe&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2yg5tciMTYcpIlv5ajIaBUjegMV" />
  <component name="ProjectViewState">
    <option name="foldersAlwaysOnTop" value="false" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;/Users/<USER>/works/galaxy/migrate/vue-element-admin-new/node_modules/typescript/lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-WS-251.26094.131" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1e2d6211-88d6-47d9-a1a2-6fd385302316" name="Changes" comment="" />
      <created>1750243591485</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750243591485</updated>
      <workItem from="1750243592793" duration="5476000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
    <option name="exactExcludedFiles">
      <list>
        <option value="$PROJECT_DIR$/src/main.js" />
        <option value="$PROJECT_DIR$/src/main.js" />
        <option value="$PROJECT_DIR$/src/main.js" />
        <option value="$PROJECT_DIR$/src/main.js" />
        <option value="$PROJECT_DIR$/src/main.js" />
        <option value="$PROJECT_DIR$/src/main.js" />
        <option value="$PROJECT_DIR$/src/main.js" />
        <option value="$PROJECT_DIR$/src/main.js" />
      </list>
    </option>
  </component>
</project>