/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BackToTop: typeof import('./components/BackToTop/index.vue')['default']
    BaseHeader: typeof import('./components/layouts/BaseHeader.vue')['default']
    BaseSide: typeof import('./components/layouts/BaseSide.vue')['default']
    Breadcrumb: typeof import('./components/Breadcrumb/index.vue')['default']
    DndList: typeof import('./components/DndList/index.vue')['default']
    DragSelect: typeof import('./components/DragSelect/index.vue')['default']
    DropdownMenu: typeof import('./components/Share/DropdownMenu.vue')['default']
    Dropzone: typeof import('./components/Dropzone/index.vue')['default']
    EditorImage: typeof import('./components/Tinymce/components/EditorImage.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElMenuItemGroup: typeof import('element-plus/es')['ElMenuItemGroup']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTag: typeof import('element-plus/es')['ElTag']
    ErrorLog: typeof import('./components/ErrorLog/index.vue')['default']
    GithubCorner: typeof import('./components/GithubCorner/index.vue')['default']
    Hamburger: typeof import('./components/Hamburger/index.vue')['default']
    HeaderSearch: typeof import('./components/HeaderSearch/index.vue')['default']
    HelloWorld: typeof import('./components/HelloWorld.vue')['default']
    ImageCropper: typeof import('./components/ImageCropper/index.vue')['default']
    JsonEditor: typeof import('./components/JsonEditor/index.vue')['default']
    Kanban: typeof import('./components/Kanban/index.vue')['default']
    Keyboard: typeof import('./components/Charts/Keyboard.vue')['default']
    LineMarker: typeof import('./components/Charts/LineMarker.vue')['default']
    Logos: typeof import('./components/Logos.vue')['default']
    Mallki: typeof import('./components/TextHoverEffect/Mallki.vue')['default']
    MarkdownEditor: typeof import('./components/MarkdownEditor/index.vue')['default']
    MDinput: typeof import('./components/MDinput/index.vue')['default']
    MessageBoxDemo: typeof import('./components/MessageBoxDemo.vue')['default']
    MixChart: typeof import('./components/Charts/MixChart.vue')['default']
    Pagination: typeof import('./components/Pagination/index.vue')['default']
    PanThumb: typeof import('./components/PanThumb/index.vue')['default']
    RightPanel: typeof import('./components/RightPanel/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Screenfull: typeof import('./components/Screenfull/index.vue')['default']
    SingleImage: typeof import('./components/Upload/SingleImage.vue')['default']
    SingleImage2: typeof import('./components/Upload/SingleImage2.vue')['default']
    SingleImage3: typeof import('./components/Upload/SingleImage3.vue')['default']
    SizeSelect: typeof import('./components/SizeSelect/index.vue')['default']
    Sticky: typeof import('./components/Sticky/index.vue')['default']
    SvgIcon: typeof import('./components/SvgIcon/index.vue')['default']
    ThemePicker: typeof import('./components/ThemePicker/index.vue')['default']
    Tinymce: typeof import('./components/Tinymce/index.vue')['default']
    UploadExcel: typeof import('./components/UploadExcel/index.vue')['default']
  }
}
