{"timestamp": "2025-06-18T10:34:35.087Z", "projectPath": "/Users/<USER>/works/galaxy/migrate/vue-element-admin-test", "options": {"skipESLint": true, "skipBuild": true, "buildCommand": "npm run build"}, "stats": {"startTime": 1750242857372, "endTime": 1750242875086, "totalSteps": 7, "completedSteps": 8, "success": true, "errors": [], "stepResults": {"packageUpgrade": {"upgraded": [{"name": "vue", "oldVersion": "2.6.10", "newVersion": "^3.4.0", "type": "dependencies"}, {"name": "vue-count-to", "oldVersion": "1.0.13", "newVersion": "^1.0.13", "type": "dependencies"}, {"name": "vue-router", "oldVersion": "3.0.2", "newVersion": "^4.5.0", "type": "dependencies"}, {"name": "vue-splitpane", "oldVersion": "1.0.4", "newVersion": "^1.0.6", "type": "dependencies"}, {"name": "vuedraggable", "oldVersion": "2.20.0", "newVersion": "^4.1.0", "type": "dependencies"}, {"name": "vuex", "oldVersion": "3.1.0", "newVersion": "^4.1.0", "type": "dependencies"}, {"name": "@vue/cli-plugin-babel", "oldVersion": "4.4.4", "newVersion": "^5.0.8", "type": "devDependencies"}, {"name": "@vue/cli-plugin-eslint", "oldVersion": "4.4.4", "newVersion": "^5.0.8", "type": "devDependencies"}, {"name": "@vue/cli-service", "oldVersion": "4.4.4", "newVersion": "^5.0.8", "type": "devDependencies"}, {"name": "@vue/test-utils", "oldVersion": "1.0.0-beta.29", "newVersion": "^2.4.6", "type": "devDependencies"}, {"name": "eslint-plugin-vue", "oldVersion": "6.2.2", "newVersion": "^10.2.0", "type": "devDependencies"}], "added": [{"name": "element-plus", "version": "^2.9.0", "type": "dependencies"}, {"name": "@element-plus/icons-vue", "version": "^2.3.1", "type": "dependencies"}, {"name": "@vue/compiler-sfc", "version": "^3.4.0", "type": "dependencies"}], "removed": [{"name": "element-ui", "type": "dependencies"}, {"name": "vue-template-compiler", "type": "devDependencies"}], "unchanged": [{"name": "axios", "version": "0.18.1", "type": "dependencies"}, {"name": "clipboard", "version": "2.0.4", "type": "dependencies"}, {"name": "codemirror", "version": "5.45.0", "type": "dependencies"}, {"name": "core-js", "version": "3.6.5", "type": "dependencies"}, {"name": "driver.js", "version": "0.9.5", "type": "dependencies"}, {"name": "dropzone", "version": "5.5.1", "type": "dependencies"}, {"name": "echarts", "version": "4.2.1", "type": "dependencies"}, {"name": "file-saver", "version": "2.0.1", "type": "dependencies"}, {"name": "fuse.js", "version": "3.4.4", "type": "dependencies"}, {"name": "js-cookie", "version": "2.2.0", "type": "dependencies"}, {"name": "jsonlint", "version": "1.6.3", "type": "dependencies"}, {"name": "j<PERSON><PERSON>", "version": "3.2.1", "type": "dependencies"}, {"name": "normalize.css", "version": "7.0.0", "type": "dependencies"}, {"name": "nprogress", "version": "0.2.0", "type": "dependencies"}, {"name": "path-to-regexp", "version": "2.4.0", "type": "dependencies"}, {"name": "screenfull", "version": "4.2.0", "type": "dependencies"}, {"name": "script-loader", "version": "0.7.2", "type": "dependencies"}, {"name": "sortablejs", "version": "1.8.4", "type": "dependencies"}, {"name": "tui-editor", "version": "1.3.3", "type": "dependencies"}, {"name": "xlsx", "version": "0.14.1", "type": "dependencies"}, {"name": "@vue/cli-plugin-unit-jest", "version": "4.4.4", "type": "devDependencies"}, {"name": "autoprefixer", "version": "9.5.1", "type": "devDependencies"}, {"name": "babel-es<PERSON>", "version": "10.1.0", "type": "devDependencies"}, {"name": "babel-jest", "version": "23.6.0", "type": "devDependencies"}, {"name": "babel-plugin-dynamic-import-node", "version": "2.3.3", "type": "devDependencies"}, {"name": "chalk", "version": "2.4.2", "type": "devDependencies"}, {"name": "chokidar", "version": "2.1.5", "type": "devDependencies"}, {"name": "connect", "version": "3.6.6", "type": "devDependencies"}, {"name": "eslint", "version": "6.7.2", "type": "devDependencies"}, {"name": "html-webpack-plugin", "version": "3.2.0", "type": "devDependencies"}, {"name": "husky", "version": "1.3.1", "type": "devDependencies"}, {"name": "lint-staged", "version": "8.1.5", "type": "devDependencies"}, {"name": "mockjs", "version": "1.0.1-beta3", "type": "devDependencies"}, {"name": "plop", "version": "2.3.0", "type": "devDependencies"}, {"name": "runjs", "version": "4.3.2", "type": "devDependencies"}, {"name": "sass", "version": "1.26.2", "type": "devDependencies"}, {"name": "sass-loader", "version": "8.0.2", "type": "devDependencies"}, {"name": "script-ext-html-webpack-plugin", "version": "2.1.3", "type": "devDependencies"}, {"name": "serve-static", "version": "1.13.2", "type": "devDependencies"}, {"name": "svg-sprite-loader", "version": "4.1.3", "type": "devDependencies"}, {"name": "svgo", "version": "1.2.0", "type": "devDependencies"}], "dependencyMapping": {"updated": 3, "dependencies": [{"source": "vue-count-to", "sourceVersion": "^1.0.13", "target": "vue3-count-to", "link": "https://github.com/xiaofan9/vue-count-to", "docPath": "/Users/<USER>/works/galaxy/migrate/migrate-cli/src/migrator/docs/vue-count-to.md", "oldVersion": "^1.0.13", "newVersion": "latest", "section": "dependencies"}, {"source": "vue-splitpane", "sourceVersion": "^1.0.6", "target": "splitpanes", "link": "https://github.com/antoniandre/splitpanes", "docPath": "/Users/<USER>/works/galaxy/migrate/migrate-cli/src/migrator/docs/vue-splitpane.md", "oldVersion": "^1.0.6", "newVersion": "latest", "section": "dependencies"}, {"source": "vuedraggable", "sourceVersion": "^4.1.0", "target": "vue.draggable.next", "link": "https://github.com/SortableJS/vue.draggable.next", "docPath": "/Users/<USER>/works/galaxy/migrate/migrate-cli/src/migrator/docs/vuedraggable.md", "oldVersion": "^4.1.0", "newVersion": "latest", "section": "dependencies"}], "packageJson": {"name": "vue-element-admin", "version": "4.4.0", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "Pan <<EMAIL>>", "scripts": {"dev": "vue-cli-service serve", "lint": "eslint --ext .js,.vue src", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "new": "plop", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"axios": "0.18.1", "clipboard": "2.0.4", "codemirror": "5.45.0", "core-js": "3.6.5", "driver.js": "0.9.5", "dropzone": "5.5.1", "echarts": "4.2.1", "file-saver": "2.0.1", "fuse.js": "3.4.4", "js-cookie": "2.2.0", "jsonlint": "1.6.3", "jszip": "3.2.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "screenfull": "4.2.0", "script-loader": "0.7.2", "sortablejs": "1.8.4", "tui-editor": "1.3.3", "vue": "^3.4.0", "vue-router": "^4.5.0", "vuex": "^4.1.0", "xlsx": "0.14.1", "element-plus": "^2.9.0", "@element-plus/icons-vue": "^2.3.1", "@vue/compiler-sfc": "^3.4.0", "vue3-count-to": "latest", "splitpanes": "latest", "vue.draggable.next": "latest"}, "devDependencies": {"@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "^5.0.8", "@vue/test-utils": "^2.4.6", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "eslint": "6.7.2", "eslint-plugin-vue": "^10.2.0", "html-webpack-plugin": "3.2.0", "husky": "1.3.1", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "plop": "2.3.0", "runjs": "4.3.2", "sass": "1.26.2", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0"}, "browserslist": ["> 1%", "last 2 versions"], "bugs": {"url": "https://github.com/PanJiaChen/vue-element-admin/issues"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "license": "MIT", "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "repository": {"type": "git", "url": "git+https://github.com/PanJiaChen/vue-element-admin.git"}}}}, "dependencyCheck": {"compatible": 55, "incompatible": 0, "unknown": 0, "total": 55}, "codeMigration": {"success": 145, "failed": 0, "skipped": 51, "copied": 0, "total": 196, "failedFiles": []}, "dependencyCodeMigration": {"totalFiles": 0, "migratedFiles": 0, "errors": []}, "failureLogging": {"failedCount": 0}}, "duration": 17714}, "success": true, "duration": 17714, "recommendations": ["运行构建命令检查剩余的构建错误", "运行 npm install 安装新依赖", "运行测试确保功能正常", "检查 UI 组件是否正确迁移到 Element Plus", "更新文档和部署配置"]}