# Vue 2 到 Vue 3 迁移指导文档

> 项目: vue-element-admin  
> 生成时间: 2025/6/18 18:34:35  
> 工具版本: vue-migrator v1.0.0

本文档基于项目分析结果自动生成，提供详细的 Vue 2 到 Vue 3 迁移指导。

## 📋 目录

- [迁移概览](#迁移概览)
- [依赖迁移指南](#依赖迁移指南)
- [代码迁移指南](#代码迁移指南)
- [分步迁移指南](#分步迁移指南)
- [常见问题](#常见问题)
- [参考资源](#参考资源)

## 📊 迁移概览

**依赖总数**: 54
**有迁移文档的依赖**: 4
**无迁移文档的依赖**: 50

**提示**: 迁移时间因项目复杂度、团队经验和依赖兼容性而异。建议先仔细评估项目特点再制定详细的迁移计划。

该迁移将涉及以下多个方面:
* Vue 核心库升级 (Vue 2.x → Vue 3.x)
* Vue Router 和 Vuex 升级
* UI 组件库升级或替换
* 代码结构和语法调整


## 📚 可用迁移文档

本工具包含以下组件的详细迁移指导文档：

- **riophae-vue-treeselect**: riophae-vue-treeselect
- **tinymce-tinymce-vue**: 从 @tinymce/tinymce-vue (Vue 2) 迁移到 @tinymce/tinymce-vue (Vue 3)
- **v-charts**: v-charts
- **vue-calendar-component**: vue-calendar-component
- **vue-count-to**: vue-count-to
- **vue-json-pretty**: 迁移 vue-json-pretty
---

# 迁移指南: `vue-json-pretty`

本指南详细介绍了将 `vue-json-pretty` 从 Vue 2 项目迁移到 Vue 3 项目的过程。该库为 Vue 的每个版本提供了不同的主版本，因此迁移主要涉及更新包版本和适应一些 API 的更改。

## 主要变化

1.  **NPM 包版本**: Vue 2 项目使用 `v1.x` 版本的 `vue-json-pretty`，而 Vue 3 项目应使用最新的 `v2.x` 或更高版本。
2.  **事件名称**: 事件名称已从 kebab-case (`node-click`) 更新为 camelCase (`nodeClick`)，以更好地与现代 JavaScript 约定保持一致。
3.  **`.sync` 修饰符**: 已弃用的 `.sync` 修饰符（用于像 `selectedValue` 这样的 props）已被 `v-model` 替换。
4.  **CSS 导入**: 手动导入样式表的需求保持不变。

---

## 分步迁移指南

### 1. 更新 `package.json`

第一步是更新 `package.json` 文件中 `vue-json-pretty` 的版本，并安装新版本。

**对于 Vue 2，你的 `package.json` 会是这样:**
```json
- **vue-scrollbars**: 从 vue-scrollbars 迁移到 vue3-perfect-scrollbar
- **vue-splitpane**: vue-splitpane
- **vue-template-compiler**: 迁移 vue-template-compiler
---

# 迁移指南: `vue-template-compiler` 到 `@vue/compiler-sfc`

本指南解释了从 `vue-template-compiler` 到 `@vue/compiler-sfc` 的迁移。与其他关注 UI 组件的指南不同，本指南处理的是 Vue 构建过程的核心部分。对于许多使用标准工具（如 Vue CLI 或 Vite）的开发者来说，此更改将在项目升级过程中自动处理。

然而，如果你有一个自定义的构建设置（例如，一个自定义的 Webpack 配置），理解这个变化是至关重要的。

## 编译器的角色

-   在 **Vue 2** 中，`vue-template-compiler` 是一个用于将 `.vue` 文件中的 `<template>` 块编译成 JavaScript `render` 函数的包。它的版本必须与核心 `vue` 包保持同步。

-   在 **Vue 3** 中，这个职责被分开了。新的 `@vue/compiler-sfc` 包负责解析单文件组件（SFCs），而实际的模板编译则由 `@vue/compiler-dom` 处理，它现在是 `vue` 包本身的核心依赖项。

从本质上讲，`@vue/compiler-sfc` 是构建工具用来理解和处理 `.vue` 文件的工具。

---

## 分步迁移指南

### 1. 依赖更新

第一步是调整你项目的 `devDependencies`。

1.  **移除 `vue-template-compiler`**: 在 Vue 3 项目中不再需要这个包。

    ```bash
    npm uninstall vue-template-compiler
    ```

2.  **检查 `@vue/compiler-sfc`**: 这个包是现代 Vue 构建插件（Webpack 的 `vue-loader`，Vite 的 `@vitejs/plugin-vue`）的对等依赖（peer dependency）。在大多数情况下，你 **不** 需要手动安装它。它会随着构建插件一起被安装。如果由于特定原因需要添加它，它应该是一个 `devDependency`。

    ```bash
    # 仅在你的特定设置需要时
    npm install -D @vue/compiler-sfc
    ```

### 2. 构建配置更新

这是最可能发生重大手动更改的地方，具体取决于你的构建工具。

#### 对于 Vue CLI 用户

当你使用官方迁移命令（`vue upgrade`）将项目升级到 Vue 3 时，Vue CLI 会自动更新你的 Webpack 配置、`vue-loader` 以及所有相关依赖。这个变更会自动为你处理。

#### 对于 Vite 用户

如果你正在使用 Vite 创建一个新的 Vue 3 项目或迁移现有项目，`@vitejs/plugin-vue` 插件会处理 SFC 编译。它在底层使用 `@vue/compiler-sfc`，你无需进行任何手动配置。

#### 对于自定义 Webpack 用户

如果你管理自己的 Webpack 配置，你需要进行以下更改：

1.  **更新 `vue-loader`**: 你必须将 `vue-loader` 更新到版本 16 或更高。版本 16 是为 Vue 3 设计的，并使用 `@vue/compiler-sfc`。

    ```bash
    npm install -D vue-loader@^16.0.0
    ```

2.  **更新 Webpack 配置**: `vue-loader` 插件的导入方式已更改。

    **Vue 2 `webpack.config.js`:**
    ```javascript
- **vue-text-format**: 迁移 vue-text-format
---

# 迁移指南: `vue-text-format`

本指南将引导你将 `vue-text-format` 从 Vue 2 项目迁移到 Vue 3 项目。

`vue-text-format` 是一个提供强大文本格式化功能的库，类似于 Excel 等电子表格应用程序中的功能。幸运的是，该库与 Vue 2 和 Vue 3 都兼容，使得迁移过程相对简单。主要的变化在于更新应用程序入口文件中插件的注册方式。

## 主要变化

1.  **NPM 包**: npm 包名保持不变：`vue-text-format`。你的 `package.json` 中关于包名的部分无需更改，但应确保你使用的是与 Vue 3 兼容的版本。
2.  **插件注册**: 插件的注册方法从 Vue 2 中的 `Vue.use()` 变为 Vue 3 中的 `app.use()`。
3.  **API 用法**: 指令 `v-format` 和全局函数 `$textFormat` 仍然可用。在组合式 API 中访问全局函数的方式略有改变。

---

## 分步迁移指南

### 1. 更新 `main.js` 中的插件注册

主要的变化在你的 `src/main.js` (或 `src/main.ts`) 文件中。你需要从 Vue 2 的插件注册语法切换到 Vue 3 的基于实例的方法。

#### Vue 2 示例 (`main.js`)

在你的 Vue 2 应用中，你会像这样注册插件：

```javascript
- **vue-uuid**: 从 vue-uuid 迁移到 vue3-uuid
- **vue2-tree-org**: 从 vue2-tree-org 迁移到 vue3-tree-org
---

# 迁移指南: `vue2-tree-org` 到 `vue3-tree-org`

本指南旨在帮助你从 `vue2-org-tree` 迁移到其指定的 Vue 3 替代品 `vue3-tree-org`。这两个包都用于渲染组织结构图，但它们是为不同主要版本的 Vue 构建的。

迁移涉及更新包、更改注册方法以及适应新组件的 API。

**重要提示:** `vue3-tree-org` 的文档主要是中文，并托管在一个独立的网站上。本指南基于项目 README 中可用的信息。你可能需要参考[官方 `vue3-tree-org` 文档](https://sangtian152.github.io/vue3-tree-org/)以获取有关 props、事件和插槽的详细 API 细节。

## 主要变化

1.  **NPM 包**: 你将从 `vue2-org-tree` 包切换到 `vue3-tree-org`。
2.  **插件注册**: 初始化从 `Vue.use()` 更改为 `app.use()`。
3.  **CSS 导入**: `vue3-tree-org` 要求你手动导入其样式表。
4.  **组件 API**: 两个版本之间的 props、事件和插槽用法已发生变化。

---

## 分步迁移指南

### 1. 更新依赖

首先，卸载 Vue 2 的包并安装 Vue 3 的包。

```bash
npm uninstall vue2-org-tree
npm install vue3-tree-org
```

### 2. 更新插件注册

在你的应用程序入口文件 (`src/main.js` 或 `src/main.ts`) 中，更新插件的注册方式并导入所需的 CSS。

#### Vue 2 示例 (`main.js`)
```javascript
- **vuedraggable**: vuedraggable
- **vuepdf**: 迁移 vuepdf 到 vue3-pdfjs
---

# 迁移指南: `vuepdf` 到 `vue3-pdfjs`

本指南概述了将 `vue-pdf` (一个流行的 Vue 2 PDF 查看器组件) 迁移到 `vue3-pdfjs` 以用于 Vue 3 应用程序的步骤。

**重要提示:** 目标库 `vue3-pdfjs` 已有数年未积极维护。虽然本指南提供了迁移所需的步骤，但你应考虑为 Vue 3 使用更现代且积极维护的替代品，例如 **`@tato30/vue-pdf`** 或 **`pdf-vue3`**。这些库提供更好的支持、更多功能，并且更可能与最新的 Web 标准和 Vue 3 功能兼容。

## 主要差异

- **包名**: 你将从 `vue-pdf` 切换到 `vue3-pdfjs`。
- **插件注册**: `vue3-pdfjs` 需要在你的 Vue 3 应用程序中注册为插件。
- **组件名称**: 组件标签从 `<pdf>` 更改为 `<VuePdf>`。
- **API 和导入**: 两个库都使用 `createLoadingTask` 函数，但导入路径以及你与之交互的方式不同，尤其是在使用组合式 API 时。

---

## 分步迁移指南

### 1. 更新依赖

首先，卸载旧包并安装新包。

```bash
npm uninstall vue-pdf
npm install vue3-pdfjs
```

### 2. 注册 Vue 3 插件

在你的应用程序入口文件 (例如, `src/main.js`) 中，你需要注册 `vue3-pdfjs`。

```javascript
// src/main.js
- **wangeditor-editor-for-vue**: 从 @wangeditor/editor-for-vue 迁移到 @wangeditor/editor-for-vue@next

总计 15 个组件的迁移文档可用。

## 📦 依赖迁移指南

### 需要迁移的依赖

以下是检测到的需要迁移的依赖及其处理方案：

#### 📋 其他组件

##### vuedraggable

- **当前版本**: 2.20.0
- **使用位置**: 0 处

**迁移指导**:

**目标包**: `vue.draggable.next`
**安装命令**:
```bash
npm uninstall vuedraggable
npm install vuedraggable@next
```
**主要变化**:
- - 安装: 包名发生了变化。
- 模板语法: 最大的变化在于如何渲染列表。`vue.draggable.next` 使用了作用域插槽（scoped slot）的 `item` 来代替 `v-for`。
- `item-key` 属性: 新增了 `item-key` 属性，用于指定列表中每个元素的唯一标识，这对于 Vue 的渲染性能至关重要。
- `transition-group` 集成: 与 `<transition-group>` 的集成方式有所改变。

📖 [详细文档](https://github.com/SortableJS/vue.draggable.next)

##### 📋 其他 其他组件依赖

以下 39 个依赖暂无专门的迁移文档，请参考通用迁移建议：

- **axios** (0.18.1)
- **clipboard** (2.0.4)
- **core-js** (3.6.5)
- **driver.js** (0.9.5)
- **dropzone** (5.5.1)
- **file-saver** (2.0.1)
- **fuse.js** (3.4.4)
- **js-cookie** (2.2.0)
- **jsonlint** (1.6.3)
- **jszip** (3.2.1)
- **normalize.css** (7.0.0)
- **nprogress** (0.2.0)
- **path-to-regexp** (2.4.0)
- **screenfull** (4.2.0)
- **script-loader** (0.7.2)
- **sortablejs** (1.8.4)
- **vue** (2.6.10)
- **xlsx** (0.14.1)
- **autoprefixer** (9.5.1)
- **babel-eslint** (10.1.0)
- **babel-jest** (23.6.0)
- **babel-plugin-dynamic-import-node** (2.3.3)
- **chalk** (2.4.2)
- **chokidar** (2.1.5)
- **connect** (3.6.6)
- **eslint** (6.7.2)
- **eslint-plugin-vue** (6.2.2)
- **html-webpack-plugin** (3.2.0)
- **husky** (1.3.1)
- **lint-staged** (8.1.5)
- **mockjs** (1.0.1-beta3)
- **plop** (2.3.0)
- **runjs** (4.3.2)
- **sass** (1.26.2)
- **sass-loader** (8.0.2)
- **script-ext-html-webpack-plugin** (2.1.3)
- **serve-static** (1.13.2)
- **svg-sprite-loader** (4.1.3)
- **svgo** (1.2.0)

**建议操作**:
1. 访问 [undefined 官方文档](https://www.npmjs.com/package/undefined)
2. 检查是否支持 Vue 3
3. 如不支持，寻找 Vue 3 兼容的替代方案


#### ✏️ 编辑器组件

##### 📋 其他 编辑器组件依赖

以下 2 个依赖暂无专门的迁移文档，请参考通用迁移建议：

- **codemirror** (5.45.0)
- **tui-editor** (1.3.3)

**建议操作**:
1. 访问 [undefined 官方文档](https://www.npmjs.com/package/undefined)
2. 检查是否支持 Vue 3
3. 如不支持，寻找 Vue 3 兼容的替代方案


#### 📊 图表组件

##### 📋 其他 图表组件依赖

以下 1 个依赖暂无专门的迁移文档，请参考通用迁移建议：

- **echarts** (4.2.1)

**建议操作**:
1. 访问 [undefined 官方文档](https://www.npmjs.com/package/undefined)
2. 检查是否支持 Vue 3
3. 如不支持，寻找 Vue 3 兼容的替代方案


#### 🎨 UI 组件库

##### 📋 其他 UI 组件库依赖

以下 1 个依赖暂无专门的迁移文档，请参考通用迁移建议：

- **element-ui** (2.13.2)

**建议操作**:
1. 查找该组件库的 Vue 3 版本
2. 如果没有 Vue 3 版本，寻找替代方案
3. 更新组件的导入和使用方式


#### 🛠️ 工具组件

##### vue-count-to

- **当前版本**: 1.0.13
- **使用位置**: 0 处

**迁移指导**:

**目标包**: `vue3-count-to`
**安装命令**:
```bash
npm uninstall vue-count-to
npm install vue3-count-to
```
**主要变化**:
- - Vue 3 支持: `vue3-count-to` 完全兼容 Vue 3。
- API 兼容性: 大部分 `vue-count-to` 的属性和方法在 `vue3-count-to` 中都得到了保留。

📖 [详细文档](https://github.com/xiaofan9/vue-count-to)

##### vue-splitpane

- **当前版本**: 1.0.4
- **使用位置**: 0 处

**迁移指导**:

**目标包**: `splitpanes`
**安装命令**:
```bash
npm uninstall vue-splitpane
npm install splitpanes
```
**主要变化**:
- - 组件化: `splitpanes` 采用更现代的组件化 API，使用 `<splitpanes>` 作为容器，`<pane>` 作为独立的窗格组件。
- 功能更丰富: `splitpanes` 提供了更多功能，如水平/垂直分割、RTL 支持、事件监听、窗格最小/最大尺寸限制等。
- Vue 3 支持: `splitpanes` 为 Vue 3 提供了原生支持。

📖 [详细文档](https://github.com/antoniandre/splitpanes)

##### vue-template-compiler

- **当前版本**: 2.6.10
- **使用位置**: 0 处

**迁移指导**:

请参考 `migrate-cli/src/migrator/docs/vue-template-compiler.md` 获取详细迁移指导。

##### 📋 其他 工具组件依赖

以下 5 个依赖暂无专门的迁移文档，请参考通用迁移建议：

- **@vue/cli-plugin-babel** (4.4.4)
- **@vue/cli-plugin-eslint** (4.4.4)
- **@vue/cli-plugin-unit-jest** (4.4.4)
- **@vue/cli-service** (4.4.4)
- **@vue/test-utils** (1.0.0-beta.29)

**建议操作**:
1. 访问 [undefined 官方文档](https://www.npmjs.com/package/undefined)
2. 检查是否支持 Vue 3
3. 如不支持，寻找 Vue 3 兼容的替代方案


#### 🔧 Vue 核心

##### 📋 其他 Vue 核心依赖

以下 2 个依赖暂无专门的迁移文档，请参考通用迁移建议：

- **vue-router** (3.0.2)
- **vuex** (3.1.0)

**建议操作**:
1. 检查 [Vue 3 官方迁移指南](https://v3-migration.vuejs.org/)
2. 更新到 Vue 3 兼容版本
3. 使用 Vue 3 迁移构建版本进行渐进式迁移




## 💻 代码迁移指南

### 核心变更

1. **应用创建**: `new Vue()` → `createApp()`
2. **全局API**: `Vue.use()` → `app.use()`
3. **生命周期**: `beforeDestroy/destroyed` → `beforeUnmount/unmounted`
4. **事件系统**: 移除 `$on/$off`，使用事件总线或状态管理
5. **组合式API**: 推荐使用 `<script setup>` 语法

### 关键代码示例

```javascript
// Vue 2 → Vue 3 应用创建
- new Vue({ render: h => h(App) }).$mount('#app')
+ createApp(App).mount('#app')
- new Vue({ render: h => h(App) }).$mount('#app')
+ createApp(App).mount('#app')

// 插件注册
- Vue.use(SomePlugin)
+ app.use(SomePlugin)

// 生命周期钩子
- beforeDestroy() { }
+ beforeUnmount() { }
```

💡 **建议**: 优先处理有迁移文档的依赖，然后逐步更新核心代码。

## 🚀 迁移步骤

### 1. 准备阶段
- 创建迁移分支: `git checkout -b vue3-migration`
- 分析依赖兼容性（已完成）
- 制定迁移计划

### 2. 核心升级
```bash
npm install vue@next vue-router@4
npm install @vue/compat  # 兼容模式
```

### 3. 依赖迁移
- 按本报告的依赖指南逐个迁移
- 优先处理有详细文档的依赖
- Element UI → Element Plus

### 4. 代码迁移
- 从叶子组件开始迁移
- 更新生命周期钩子
- 替换事件总线

### 5. 测试验证
- 功能测试
- 性能验证
- 修复问题

## 🔧 常见问题

- **构建错误**: 检查 Vue 3 版本和构建配置
- **$on/$off 报错**: 使用事件总线库（如 mitt）替代
- **Element UI 不工作**: 迁移到 Element Plus
- **生命周期报错**: 更新钩子名称（beforeDestroy → beforeUnmount）
- **性能问题**: 检查响应式 API 使用

## 📚 参考资源

- [Vue 3 迁移指南](https://v3-migration.vuejs.org/) - 官方迁移文档
- [Element Plus](https://element-plus.org/) - Vue 3 UI 组件库
- [Vue Router 4](https://router.vuejs.org/) - Vue 3 路由
- [Pinia](https://pinia.vuejs.org/) - Vue 3 状态管理

---

*本文档由 vue-migrator 工具自动生成。如有问题，请参考官方文档或寻求社区帮助。*

**祝您迁移顺利！** 🎉