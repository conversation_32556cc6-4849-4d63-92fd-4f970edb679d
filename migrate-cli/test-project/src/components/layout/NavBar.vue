<template>
  <el-menu
    :default-active="activeIndex"
    class="nav-menu"
    mode="horizontal"
    router
    background-color="#409EFF"
    text-color="#fff"
    active-text-color="#ffd04b"
  >
    <el-menu-item index="/">
      <el-icon><el-icon-s-home /></el-icon>
      Home
    </el-menu-item>
    <el-menu-item index="/dashboard">
      <el-icon><el-icon-s-data /></el-icon>
      Dashboard
    </el-menu-item>
    <el-menu-item index="/components">
      <el-icon><el-icon-s-grid /></el-icon>
      Components
    </el-menu-item>
    <el-menu-item index="/charts">
      <el-icon><el-icon-s-marketing /></el-icon>
      Charts
    </el-menu-item>
    <el-menu-item index="/forms">
      <el-icon><el-icon-edit /></el-icon>
      Forms
    </el-menu-item>
    <el-menu-item index="/tables">
      <el-icon><el-icon-s-order /></el-icon>
      Tables
    </el-menu-item>
    <el-menu-item index="/calendar">
      <el-icon><el-icon-date /></el-icon>
      Calendar
    </el-menu-item>
    <el-menu-item index="/editor">
      <el-icon><el-icon-edit-outline /></el-icon>
      Editor
    </el-menu-item>
    <el-menu-item index="/upload">
      <el-icon><el-icon-upload /></el-icon>
      Upload
    </el-menu-item>

    <div class="nav-right">
      <el-dropdown @command="handleCommand">
        <span class="el-dropdown-link">
          <el-icon><el-icon-user /></el-icon>
          {{ currentUser ? currentUser.username : 'Guest' }}
          <el-icon class="el-icon--right"><el-icon-arrow-down /></el-icon>
        </span>
        <template v-slot:dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">Profile</el-dropdown-item>
            <el-dropdown-item command="settings">Settings</el-dropdown-item>
            <el-dropdown-item divided command="logout">Logout</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </el-menu>
</template>

<script>
import {
  SHome as ElIconSHome,
  SData as ElIconSData,
  SGrid as ElIconSGrid,
  SMarketing as ElIconSMarketing,
  Edit as ElIconEdit,
  SOrder as ElIconSOrder,
  Date as ElIconDate,
  EditOutline as ElIconEditOutline,
  Upload as ElIconUpload,
  User as ElIconUser,
  ArrowDown as ElIconArrowDown,
} from '@element-plus/icons'
import { mapGetters, mapActions } from 'vuex'

export default {
  components: {
    ElIconSHome,
    ElIconSData,
    ElIconSGrid,
    ElIconSMarketing,
    ElIconEdit,
    ElIconSOrder,
    ElIconDate,
    ElIconEditOutline,
    ElIconUpload,
    ElIconUser,
    ElIconArrowDown,
  },
  name: 'NavBar',
  data() {
    return {
      activeIndex: '/',
    }
  },
  computed: {
    ...mapGetters('user', ['currentUser', 'isAuthenticated']),
  },
  watch: {
    $route(to) {
      this.activeIndex = to.path
    },
  },
  methods: {
    ...mapActions('user', ['logout']),
    handleCommand(command) {
      switch (command) {
        case 'profile':
          this.$message.info('Profile clicked')
          break
        case 'settings':
          this.$message.info('Settings clicked')
          break
        case 'logout':
          this.logout()
          this.$router.push('/auth')
          this.$message.success('Logged out successfully')
          break
      }
    },
  },
  mounted() {
    this.activeIndex = this.$route.path
  },
}
</script>

<style scoped>
.nav-menu {
  border-bottom: none;
}
.nav-right {
  float: right;
  height: 60px;
  line-height: 60px;
  margin-right: 20px;
}
.el-dropdown-link {
  color: #fff;
  cursor: pointer;
}
.el-dropdown-link:hover {
  color: #ffd04b;
}
</style>
