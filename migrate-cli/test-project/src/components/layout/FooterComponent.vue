<template>
  <div class="footer">
    <div class="footer-content">
      <div class="footer-section">
        <h4>Vue 2 Test Project</h4>
        <p>A comprehensive test project for Vue 2 to Vue 3 migration</p>
      </div>
      <div class="footer-section">
        <h4>Links</h4>
        <ul>
          <li><a href="#" @click.prevent="goToHome">Home</a></li>
          <li><a href="#" @click.prevent="goToComponents">Components</a></li>
          <li><a href="#" @click.prevent="goToCharts">Charts</a></li>
        </ul>
      </div>
      <div class="footer-section">
        <h4>Contact</h4>
        <p>Email: <EMAIL></p>
        <p>Phone: ****** 567 890</p>
      </div>
    </div>
    <div class="footer-bottom">
      <p>&copy; 2024 Vue 2 Test Project. All rights reserved.</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FooterComponent',
  methods: {
    goToHome() {
      this.$router.push('/')
    },
    goToComponents() {
      this.$router.push('/components')
    },
    goToCharts() {
      this.$router.push('/charts')
    },
  },
}
</script>

<style scoped>
.footer {
  background-color: #f5f5f5;
  padding: 20px 0;
  color: #666;
}
.footer-content {
  display: flex;
  justify-content: space-around;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.footer-section {
  flex: 1;
  margin: 0 20px;
}
.footer-section h4 {
  color: #333;
  margin-bottom: 10px;
}
.footer-section ul {
  list-style: none;
  padding: 0;
}
.footer-section ul li {
  margin-bottom: 5px;
}
.footer-section ul li a {
  color: #666;
  text-decoration: none;
}
.footer-section ul li a:hover {
  color: #409eff;
}
.footer-bottom {
  text-align: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ddd;
}
</style>
