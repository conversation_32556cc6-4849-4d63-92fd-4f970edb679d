<template>
  <div class="components">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template v-slot:header>
            <div>
              <span>Vue 2 Components Test</span>
            </div>
          </template>

          <!-- Count To Component -->
          <el-row :gutter="20" style="margin-bottom: 20px">
            <el-col :span="12">
              <el-card>
                <template v-slot:header>
                  <div>
                    <span>Count To Component</span>
                  </div>
                </template>
                <div class="component-demo">
                  <count-to
                    :start-val="0"
                    :end-val="1000"
                    :duration="3000"
                    :autoplay="true"
                    @ready="onCountReady"
                  />
                  <el-button @click="resetCount" style="margin-left: 10px"
                    >Reset</el-button
                  >
                </div>
              </el-card>
            </el-col>

            <el-col :span="12">
              <el-card>
                <template v-slot:header>
                  <div>
                    <span>Draggable Component</span>
                  </div>
                </template>
                <div class="component-demo">
                  <draggable
                    v-model:value="draggableList"
                    @start="drag = true"
                    @end="drag = false"
                  >
                    <div
                      v-for="element in draggableList"
                      :key="element.id"
                      class="draggable-item"
                    >
                      {{ element.name }}
                    </div>
                  </draggable>
                  <p v-if="drag" class="drag-status">Drag in progress...</p>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- Split Pane Component -->
          <el-row :gutter="20" style="margin-bottom: 20px">
            <el-col :span="24">
              <el-card>
                <template v-slot:header>
                  <div>
                    <span>Split Pane Component</span>
                  </div>
                </template>
                <div class="component-demo">
                  <split-pane
                    :min-percent="20"
                    :default-percent="50"
                    split="vertical"
                  >
                    <template v-slot:paneL>
                      <div class="pane-content left-pane">
                        <h4>Left Pane</h4>
                        <p>This is the left pane content</p>
                      </div>
                    </template>
                    <template v-slot:paneR>
                      <div class="pane-content right-pane">
                        <h4>Right Pane</h4>
                        <p>This is the right pane content</p>
                      </div>
                    </template>
                  </split-pane>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- Tree Select Component -->
          <el-row :gutter="20" style="margin-bottom: 20px">
            <el-col :span="12">
              <el-card>
                <template v-slot:header>
                  <div>
                    <span>Tree Select Component</span>
                  </div>
                </template>
                <div class="component-demo">
                  <treeselect
                    v-model:value="selectedTreeValue"
                    :options="treeOptions"
                    :normalizer="normalizer"
                    placeholder="Select an item..."
                  />
                  <p>Selected: {{ selectedTreeValue }}</p>
                </div>
              </el-card>
            </el-col>

            <el-col :span="12">
              <el-card>
                <template v-slot:header>
                  <div>
                    <span>Calendar Component</span>
                  </div>
                </template>
                <div class="component-demo">
                  <calendar
                    v-model:value="selectedDate"
                    :events="calendarEvents"
                    @dayclick="onDayClick"
                  />
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- Charts Components -->
          <el-row :gutter="20" style="margin-bottom: 20px">
            <el-col :span="12">
              <el-card>
                <template v-slot:header>
                  <div>
                    <span>V-Charts Component</span>
                  </div>
                </template>
                <div class="component-demo">
                  <ve-line :data="chartData" :settings="chartSettings" />
                </div>
              </el-card>
            </el-col>

            <el-col :span="12">
              <el-card>
                <template v-slot:header>
                  <div>
                    <span>Vue ECharts Component</span>
                  </div>
                </template>
                <div class="component-demo">
                  <v-chart :options="echartOptions" style="height: 300px" />
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- JSON Pretty Component -->
          <el-row :gutter="20" style="margin-bottom: 20px">
            <el-col :span="24">
              <el-card>
                <template v-slot:header>
                  <div>
                    <span>JSON Pretty Component</span>
                  </div>
                </template>
                <div class="component-demo">
                  <vue-json-pretty
                    :data="jsonData"
                    :deep="2"
                    :show-double-quotes="true"
                    :show-length="true"
                    :show-line="true"
                  />
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- Scrollbars Component -->
          <el-row :gutter="20" style="margin-bottom: 20px">
            <el-col :span="12">
              <el-card>
                <template v-slot:header>
                  <div>
                    <span>Scrollbars Component</span>
                  </div>
                </template>
                <div class="component-demo">
                  <vue-scrollbar class="scrollbar-demo">
                    <div v-for="i in 20" :key="i" class="scroll-item">
                      Scroll item {{ i }}
                    </div>
                  </vue-scrollbar>
                </div>
              </el-card>
            </el-col>

            <el-col :span="12">
              <el-card>
                <template v-slot:header>
                  <div>
                    <span>UUID Component</span>
                  </div>
                </template>
                <div class="component-demo">
                  <p>Generated UUID: {{ generatedUuid }}</p>
                  <el-button @click="generateNewUuid"
                    >Generate New UUID</el-button
                  >
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import CountTo from 'vue-count-to'
import Draggable from 'vuedraggable'
import SplitPane from 'vue-splitpane'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import Calendar from 'vue-calendar-component'
import VeLine from 'v-charts/lib/line.common'
import VChart from 'vue-echarts'
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'
import VueScrollbar from 'vue-scrollbars'
import { v4 as uuidv4 } from 'vue-uuid'

export default {
  name: 'Components',
  components: {
    CountTo,
    Draggable,
    SplitPane,
    Treeselect,
    Calendar,
    VeLine,
    VChart,
    VueJsonPretty,
    VueScrollbar,
  },
  data() {
    return {
      drag: false,
      draggableList: [
        { id: 1, name: 'Item 1' },
        { id: 2, name: 'Item 2' },
        { id: 3, name: 'Item 3' },
        { id: 4, name: 'Item 4' },
      ],
      selectedTreeValue: null,
      treeOptions: [
        {
          id: '1',
          label: 'Category 1',
          children: [
            { id: '1-1', label: 'Subcategory 1-1' },
            { id: '1-2', label: 'Subcategory 1-2' },
          ],
        },
        {
          id: '2',
          label: 'Category 2',
          children: [
            { id: '2-1', label: 'Subcategory 2-1' },
            { id: '2-2', label: 'Subcategory 2-2' },
          ],
        },
      ],
      selectedDate: new Date(),
      calendarEvents: [
        { date: new Date(), title: 'Event 1' },
        { date: new Date(Date.now() + 86400000), title: 'Event 2' },
      ],
      chartData: {
        columns: ['date', 'value'],
        rows: [
          { date: '2024-01-01', value: 100 },
          { date: '2024-01-02', value: 200 },
          { date: '2024-01-03', value: 150 },
          { date: '2024-01-04', value: 300 },
        ],
      },
      chartSettings: {
        yAxisName: ['Value'],
      },
      echartOptions: {
        title: {
          text: 'ECharts Demo',
        },
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: 'line',
          },
        ],
      },
      jsonData: {
        name: 'Vue 2 Test Project',
        version: '1.0.0',
        description:
          'A comprehensive test project for Vue 2 to Vue 3 migration',
        dependencies: {
          vue: '^2.6.14',
          'vue-router': '^3.5.4',
          vuex: '^3.6.2',
        },
        features: ['Components', 'Charts', 'Forms', 'Tables'],
      },
      generatedUuid: '',
    }
  },
  methods: {
    onCountReady() {
      console.log('Count component is ready')
    },
    resetCount() {
      this.$refs.countTo.reset()
    },
    normalizer(node) {
      return {
        id: node.id,
        label: node.label,
        children: node.children,
      }
    },
    onDayClick(day) {
      this.$message.info(`Clicked on ${day.date}`)
    },
    generateNewUuid() {
      this.generatedUuid = uuidv4()
    },
  },
  mounted() {
    this.generateNewUuid()
  },
}
</script>

<style scoped>
.components {
  padding: 20px;
}
.component-demo {
  padding: 10px;
}
.draggable-item {
  padding: 10px;
  margin: 5px 0;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: move;
}
.drag-status {
  color: #409eff;
  font-style: italic;
}
.pane-content {
  padding: 20px;
  height: 200px;
}
.left-pane {
  background-color: #f0f9ff;
}
.right-pane {
  background-color: #f0fff4;
}
.scrollbar-demo {
  height: 200px;
}
.scroll-item {
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
}
.scroll-item:last-child {
  border-bottom: none;
}
</style>
