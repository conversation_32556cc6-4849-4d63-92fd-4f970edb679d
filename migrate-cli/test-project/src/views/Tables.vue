<template>
  <div class="tables">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template v-slot:header>
            <div>
              <span>Data Tables</span>
            </div>
          </template>

          <!-- Element UI Table -->
          <el-card style="margin-bottom: 20px">
            <template v-slot:header>
              <div>
                <span>Element UI Table</span>
              </div>
            </template>
            <el-table :data="tableData" style="width: 100%">
              <el-table-column
                prop="id"
                label="ID"
                width="80"
              ></el-table-column>
              <el-table-column
                prop="name"
                label="Name"
                width="180"
              ></el-table-column>
              <el-table-column prop="email" label="Email"></el-table-column>
              <el-table-column prop="status" label="Status" width="100">
                <template v-slot="scope">
                  <el-tag
                    :type="scope.row.status === 'active' ? 'success' : 'danger'"
                  >
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="Actions" width="150">
                <template v-slot="scope">
                  <el-button
                    size="mini"
                    @click="handleEdit(scope.$index, scope.row)"
                    >Edit</el-button
                  >
                  <el-button
                    size="mini"
                    type="danger"
                    @click="handleDelete(scope.$index, scope.row)"
                    >Delete</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-card>

          <!-- Vue Good Table -->
          <el-card style="margin-bottom: 20px">
            <template v-slot:header>
              <div>
                <span>Vue Good Table</span>
              </div>
            </template>
            <vue-good-table
              :columns="goodTableColumns"
              :rows="goodTableRows"
              :search-options="{
                enabled: true,
                placeholder: 'Search this table',
              }"
              :pagination-options="{
                enabled: true,
                mode: 'records',
              }"
            />
          </el-card>

          <!-- Vue Tables 2 -->
          <el-card style="margin-bottom: 20px">
            <template v-slot:header>
              <div>
                <span>Vue Tables 2</span>
              </div>
            </template>
            <div>
              <vue-tables-2
                :data="vueTables2Data"
                :columns="vueTables2Columns"
                :options="vueTables2Options"
              />
            </div>
          </el-card>

          <!-- Vue Data Tables -->
          <el-card>
            <template v-slot:header>
              <div>
                <span>Vue Data Tables</span>
              </div>
            </template>
            <data-tables
              :data="dataTablesData"
              :table-props="dataTablesProps"
              :pagination-props="dataTablesPagination"
            >
              <el-table-column
                prop="id"
                label="ID"
                width="80"
              ></el-table-column>
              <el-table-column
                prop="name"
                label="Name"
                width="180"
              ></el-table-column>
              <el-table-column prop="email" label="Email"></el-table-column>
              <el-table-column prop="status" label="Status" width="100">
                <template v-slot="scope">
                  <el-tag
                    :type="scope.row.status === 'active' ? 'success' : 'danger'"
                  >
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
            </data-tables>
          </el-card>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { VueGoodTable } from 'vue-good-table'
import 'vue-good-table/dist/vue-good-table.css'
import { ServerTable, ClientTable } from 'vue-tables-2'
import { DataTables, DataTablesServer } from 'vue-data-tables'

export default {
  name: 'Tables',
  components: {
    VueGoodTable,
    ServerTable,
    ClientTable,
    DataTables,
    DataTablesServer,
  },
  data() {
    return {
      tableData: [
        {
          id: 1,
          name: 'John Doe',
          email: '<EMAIL>',
          status: 'active',
        },
        {
          id: 2,
          name: 'Jane Smith',
          email: '<EMAIL>',
          status: 'inactive',
        },
        {
          id: 3,
          name: 'Bob Johnson',
          email: '<EMAIL>',
          status: 'active',
        },
        {
          id: 4,
          name: 'Alice Brown',
          email: '<EMAIL>',
          status: 'active',
        },
      ],
      goodTableColumns: [
        { label: 'ID', field: 'id', sortable: true },
        { label: 'Name', field: 'name', sortable: true },
        { label: 'Email', field: 'email', sortable: true },
        { label: 'Status', field: 'status', sortable: true },
      ],
      goodTableRows: [
        {
          id: 1,
          name: 'John Doe',
          email: '<EMAIL>',
          status: 'active',
        },
        {
          id: 2,
          name: 'Jane Smith',
          email: '<EMAIL>',
          status: 'inactive',
        },
        {
          id: 3,
          name: 'Bob Johnson',
          email: '<EMAIL>',
          status: 'active',
        },
        {
          id: 4,
          name: 'Alice Brown',
          email: '<EMAIL>',
          status: 'active',
        },
      ],
      vueTables2Columns: ['id', 'name', 'email', 'status'],
      vueTables2Data: [
        {
          id: 1,
          name: 'John Doe',
          email: '<EMAIL>',
          status: 'active',
        },
        {
          id: 2,
          name: 'Jane Smith',
          email: '<EMAIL>',
          status: 'inactive',
        },
        {
          id: 3,
          name: 'Bob Johnson',
          email: '<EMAIL>',
          status: 'active',
        },
        {
          id: 4,
          name: 'Alice Brown',
          email: '<EMAIL>',
          status: 'active',
        },
      ],
      vueTables2Options: {
        headings: {
          id: 'ID',
          name: 'Name',
          email: 'Email',
          status: 'Status',
        },
        sortable: ['id', 'name', 'email', 'status'],
        filterable: ['name', 'email', 'status'],
      },
      dataTablesData: [
        {
          id: 1,
          name: 'John Doe',
          email: '<EMAIL>',
          status: 'active',
        },
        {
          id: 2,
          name: 'Jane Smith',
          email: '<EMAIL>',
          status: 'inactive',
        },
        {
          id: 3,
          name: 'Bob Johnson',
          email: '<EMAIL>',
          status: 'active',
        },
        {
          id: 4,
          name: 'Alice Brown',
          email: '<EMAIL>',
          status: 'active',
        },
      ],
      dataTablesProps: {
        border: true,
        stripe: true,
      },
      dataTablesPagination: {
        pageSize: 10,
        pageSizes: [10, 20, 50],
      },
    }
  },
  methods: {
    handleEdit(index, row) {
      this.$message.info(`Edit row ${index}: ${row.name}`)
    },
    handleDelete(index, row) {
      this.$confirm('Are you sure you want to delete this item?', 'Warning', {
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel',
        type: 'warning',
      })
        .then(() => {
          this.tableData.splice(index, 1)
          this.$message.success('Delete successful')
        })
        .catch(() => {
          this.$message.info('Delete canceled')
        })
    },
  },
}
</script>

<style scoped>
.tables {
  padding: 20px;
}
</style>
