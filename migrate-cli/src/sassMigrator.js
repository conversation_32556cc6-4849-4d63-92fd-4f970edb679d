const fs = require('fs-extra');
const path = require('path');
const { execSync, spawn } = require('child_process');
const chalk = require('chalk');
const glob = require('glob');

/**
 * Sass 迁移器
 * 处理 Sass/SCSS 文件从 @import 到 @use 的语法迁移
 */
class SassMigrator {
  constructor(projectPath, options = {}) {
    this.projectPath = path.resolve(projectPath);
    this.options = {
      backup: true,
      verbose: false,
      dryRun: false,
      include: ['**/*.scss', '**/*.sass'],
      exclude: ['node_modules/**', 'dist/**', 'build/**'],
      ...options
    };

    this.stats = {
      totalFiles: 0,
      processedFiles: 0,
      skippedFiles: 0,
      errorFiles: 0,
      errors: []
    };
  }

  /**
   * 检查 sass-migrator 是否可用
   */
  async isSassMigratorAvailable() {
    try {
      execSync('sass-migrator --version', { stdio: 'ignore' });
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 查找所有 Sass/SCSS 文件
   */
  async findSassFiles() {
    const allFiles = [];

    for (const pattern of this.options.include) {
      const files = glob.sync(pattern, {
        cwd: this.projectPath,
        absolute: true,
        ignore: this.options.exclude
      });
      allFiles.push(...files);
    }

    // 去重并过滤已存在的文件
    const uniqueFiles = [...new Set(allFiles)];
    const existingFiles = [];

    for (const file of uniqueFiles) {
      if (await fs.pathExists(file)) {
        existingFiles.push(file);
      }
    }

    return existingFiles;
  }

  /**
   * 检查文件是否需要迁移
   */
  async needsMigration(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      
      // 检查是否包含 @import 规则
      const importRegex = /@import\s+['"][^'"]+['"];?/g;
      const hasImports = importRegex.test(content);
      
      // 只要包含 @import 语句就需要迁移
      return hasImports;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  无法读取文件: ${filePath}`));
      return false;
    }
  }

  /**
   * 备份文件
   */
  async backupFile(filePath) {
    if (!this.options.backup) {
      return null;
    }

    const backupPath = `${filePath}.sass-backup`;
    await fs.copy(filePath, backupPath);
    return backupPath;
  }

  /**
   * 使用 sass-migrator 迁移单个文件
   */
  async migrateFile(filePath) {
    try {
      console.log(chalk.gray(`  处理文件: ${path.relative(this.projectPath, filePath)}`));

      // 检查是否需要迁移
      if (!await this.needsMigration(filePath)) {
        console.log(chalk.gray(`    跳过: 文件不需要迁移`));
        this.stats.skippedFiles++;
        return { success: true, skipped: true };
      }

      // 备份文件
      let backupPath = null;
      if (this.options.backup) {
        backupPath = await this.backupFile(filePath);
        console.log(chalk.gray(`    已备份到: ${path.basename(backupPath)}`));
      }

      // 构建 sass-migrator 命令
      const args = [
        'module',  // 使用 module 迁移器
        filePath
      ];

      if (this.options.dryRun) {
        args.push('--dry-run');
      }

      if (this.options.verbose) {
        args.push('--verbose');
      }

      // 执行 sass-migrator
      await this.executeSassMigrator(args);

      console.log(chalk.green(`    ✅ 迁移成功`));
      this.stats.processedFiles++;

      return {
        success: true,
        backupPath,
        skipped: false
      };

    } catch (error) {
      console.log(chalk.red(`    ❌ 迁移失败: ${error.message}`));
      this.stats.errorFiles++;
      this.stats.errors.push({
        file: filePath,
        error: error.message
      });

      return {
        success: false,
        error: error.message,
        skipped: false
      };
    }
  }

  /**
   * 执行 sass-migrator 命令
   */
  async executeSassMigrator(args) {
    return new Promise((resolve, reject) => {
      const child = spawn('sass-migrator', args, {
        cwd: this.projectPath,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        if (code === 0) {
          resolve(stdout);
        } else {
          reject(new Error(stderr || `sass-migrator exited with code ${code}`));
        }
      });

      child.on('error', (error) => {
        reject(new Error(`Failed to start sass-migrator: ${error.message}`));
      });
    });
  }

  /**
   * 执行 Sass 迁移
   */
  async migrate() {
    console.log(chalk.blue('🎨 开始 Sass 语法迁移...'));

    try {
      // 检查 sass-migrator 是否可用
      if (!await this.isSassMigratorAvailable()) {
        throw new Error('sass-migrator 不可用，请安装: npm install -g sass-migrator');
      }

      // 查找所有 Sass 文件
      const sassFiles = await this.findSassFiles();
      this.stats.totalFiles = sassFiles.length;

      if (sassFiles.length === 0) {
        console.log(chalk.gray('未找到需要迁移的 Sass/SCSS 文件'));
        return this.stats;
      }

      console.log(chalk.blue(`找到 ${sassFiles.length} 个 Sass/SCSS 文件`));

      // 逐个处理文件
      for (const filePath of sassFiles) {
        await this.migrateFile(filePath);
      }

      // 打印统计信息
      this.printStats();

      return this.stats;

    } catch (error) {
      console.error(chalk.red(`Sass 迁移失败: ${error.message}`));
      throw error;
    }
  }

  /**
   * 打印统计信息
   */
  printStats() {
    console.log('\n' + chalk.bold('📊 Sass 迁移统计:'));
    console.log(`总文件数: ${this.stats.totalFiles}`);
    console.log(`已处理: ${this.stats.processedFiles}`);
    console.log(`已跳过: ${this.stats.skippedFiles}`);
    console.log(`错误: ${this.stats.errorFiles}`);

    if (this.stats.errors.length > 0) {
      console.log(chalk.yellow('\n⚠️  错误文件:'));
      this.stats.errors.forEach(({ file, error }) => {
        console.log(chalk.red(`  ${path.relative(this.projectPath, file)}: ${error}`));
      });
    }

    if (this.stats.processedFiles > 0) {
      console.log(chalk.green(`\n✅ ${this.stats.processedFiles} 个文件成功迁移到 @use 语法`));
    }
  }

  /**
   * 恢复备份文件
   */
  async restoreBackups() {
    try {
      const backupFiles = glob.sync('**/*.sass-backup', {
        cwd: this.projectPath,
        absolute: true
      });

      for (const backupFile of backupFiles) {
        const originalFile = backupFile.replace('.sass-backup', '');
        await fs.move(backupFile, originalFile, { overwrite: true });
        console.log(chalk.gray(`已恢复: ${path.relative(this.projectPath, originalFile)}`));
      }

      console.log(chalk.green(`✅ 已恢复 ${backupFiles.length} 个备份文件`));
    } catch (error) {
      console.error(chalk.red(`恢复备份失败: ${error.message}`));
      throw error;
    }
  }

  /**
   * 清理备份文件
   */
  async cleanBackups() {
    try {
      const backupFiles = glob.sync('**/*.sass-backup', {
        cwd: this.projectPath,
        absolute: true
      });

      for (const backupFile of backupFiles) {
        await fs.remove(backupFile);
      }

      if (backupFiles.length > 0) {
        console.log(chalk.gray(`🧹 已清理 ${backupFiles.length} 个备份文件`));
      }
    } catch (error) {
      console.warn(chalk.yellow(`清理备份文件时出错: ${error.message}`));
    }
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return { ...this.stats };
  }
}

module.exports = SassMigrator; 