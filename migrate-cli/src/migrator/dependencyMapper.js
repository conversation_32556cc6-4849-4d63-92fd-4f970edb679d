const fs = require('fs-extra');
const path = require('path');
const matter = require('gray-matter');

/**
 * 依赖映射器
 * 解析 migrator/docs 中的文档，提取 source -> target 依赖映射关系
 */
class DependencyMapper {
  constructor(docsPath) {
    this.docsPath = docsPath || path.join(__dirname, 'docs');
    this.mappings = new Map();
    this.loadMappings();
  }

  /**
   * 加载所有依赖映射
   */
  loadMappings() {
    try {
      if (!fs.existsSync(this.docsPath)) {
        console.warn(`依赖文档目录不存在: ${this.docsPath}`);
        return;
      }

      const files = fs.readdirSync(this.docsPath).filter(file => file.endsWith('.md'));
      
      for (const file of files) {
        const filePath = path.join(this.docsPath, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        try {
          const { data } = matter(content);
          
          if (data.source && data.target) {
            // 处理 source 字段包含多个依赖名的情况（用逗号分隔）
            const sources = data.source.split(',').map(s => s.trim());
            
            for (const source of sources) {
              if (source) {
                this.mappings.set(source, {
                  target: data.target,
                  link: data.link,
                  docPath: filePath,
                  docContent: content
                });
              }
            }
          }
        } catch (error) {
          console.warn(`解析文档失败: ${file}`, error.message);
        }
      }

      console.log(`✅ 加载了 ${this.mappings.size} 个依赖映射`);
    } catch (error) {
      console.error('加载依赖映射失败:', error.message);
    }
  }

  /**
   * 获取源依赖对应的目标依赖
   */
  getTargetDependency(sourceDep) {
    return this.mappings.get(sourceDep);
  }

  /**
   * 检查是否有映射
   */
  hasMapping(sourceDep) {
    return this.mappings.has(sourceDep);
  }

  /**
   * 获取所有映射
   */
  getAllMappings() {
    return Array.from(this.mappings.entries()).map(([source, info]) => ({
      source,
      target: info.target,
      link: info.link,
      docPath: info.docPath
    }));
  }

  /**
   * 获取需要迁移的依赖
   */
  getMigrationDependencies(packageJsonPath) {
    try {
      const packageJson = fs.readJsonSync(packageJsonPath);
      const allDeps = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies
      };

      const migrationDeps = [];

      // 检查所有映射，看看哪些源依赖存在于 package.json 中
      for (const [source, mapping] of this.mappings.entries()) {
        if (allDeps[source]) {
          migrationDeps.push({
            source,
            sourceVersion: allDeps[source],
            target: mapping.target,
            link: mapping.link,
            docPath: mapping.docPath
          });
        }
      }

      return migrationDeps;
    } catch (error) {
      console.error('获取迁移依赖失败:', error.message);
      return [];
    }
  }

  /**
   * 更新 package.json 中的依赖
   */
  async updatePackageJsonDependencies(packageJsonPath, dryRun = false) {
    try {
      const packageJson = fs.readJsonSync(packageJsonPath);
      const migrationDeps = this.getMigrationDependencies(packageJsonPath);
      
      if (migrationDeps.length === 0) {
        console.log('📦 没有需要迁移的依赖');
        return { updated: 0, dependencies: [] };
      }

      let updated = 0;
      const updatedDeps = [];

      // 更新 dependencies
      if (packageJson.dependencies) {
        for (const migration of migrationDeps) {
          if (packageJson.dependencies[migration.source]) {
            const oldVersion = packageJson.dependencies[migration.source];
            delete packageJson.dependencies[migration.source];
            packageJson.dependencies[migration.target] = 'latest'; // 或者从文档中获取推荐版本
            
            updated++;
            updatedDeps.push({
              ...migration,
              oldVersion,
              newVersion: 'latest',
              section: 'dependencies'
            });
          }
        }
      }

      // 更新 devDependencies
      if (packageJson.devDependencies) {
        for (const migration of migrationDeps) {
          if (packageJson.devDependencies[migration.source]) {
            const oldVersion = packageJson.devDependencies[migration.source];
            delete packageJson.devDependencies[migration.source];
            packageJson.devDependencies[migration.target] = 'latest';
            
            updated++;
            updatedDeps.push({
              ...migration,
              oldVersion,
              newVersion: 'latest',
              section: 'devDependencies'
            });
          }
        }
      }

      if (!dryRun && updated > 0) {
        // 备份原文件
        const backupPath = packageJsonPath + '.backup';
        if (!fs.existsSync(backupPath)) {
          fs.copySync(packageJsonPath, backupPath);
        }

        // 写入更新后的 package.json
        fs.writeJsonSync(packageJsonPath, packageJson, { spaces: 2 });
      }

      return {
        updated,
        dependencies: updatedDeps,
        packageJson: dryRun ? null : packageJson
      };

    } catch (error) {
      console.error('更新 package.json 失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取依赖的迁移文档内容
   */
  getMigrationDoc(sourceDep) {
    const mapping = this.mappings.get(sourceDep);
    return mapping ? mapping.docContent : null;
  }
}

module.exports = DependencyMapper;
