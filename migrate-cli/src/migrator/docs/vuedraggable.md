---
source: vuedraggable
target: vue-draggable-next
link: https://github.com/SortableJS/vue-draggable-next
---

# 从 vuedraggable 迁移到 vue-draggable-next

`vue-draggable-next` 是 `vuedraggable` 针对 Vue 3 的官方升级版本，它基于强大的 `Sortable.js`。迁移到新版本需要对代码进行一些重要的修改，主要是关于模板的写法。

## 主要变化

- **安装**: 包名发生了变化。
- **模板语法**: 最大的变化在于如何渲染列表。`vue-draggable-next` 使用了作用域插槽（scoped slot）的 `item` 来代替 `v-for`。
- **`item-key` 属性**: 新增了 `item-key` 属性，用于指定列表中每个元素的唯一标识，这对于 Vue 的渲染性能至关重要。
- **`transition-group` 集成**: 与 `<transition-group>` 的集成方式有所改变。

## 安装

首先，你需要卸载旧版本并安装新版本：

```bash
npm uninstall vuedraggable
npm install vuedraggable@next
```

或者使用 yarn:

```bash
yarn remove vuedraggable
yarn add vuedraggable@next
```

## 使用方法

### 1. 导入组件

在你的组件中导入 `draggable` 组件：

```vue

<script setup>
  import draggable from 'src/migrator/docs/vuedraggable'
</script>
```

### 2. 基本用法迁移

这是迁移过程中最核心的部分。旧的 `vuedraggable` 允许你在组件内部直接使用 `v-for`。而 `vue-draggable-next` 则要求你使用 `#item` 插槽。

**Vue 2 with `vuedraggable`:**
```html
<draggable v-model="myArray" group="people">
  <div v-for="element in myArray" :key="element.id">
    {{ element.name }}
  </div>
</draggable>
```

**Vue 3 with `vue-draggable-next`:**

```vue

<template>
  <draggable
      v-model="myArray"
      group="people"
      @start="drag=true"
      @end="drag=false"
      item-key="id">
    <template #item="{element}">
      <div>{{element.name}}</div>
    </template>
  </draggable>
</template>

<script setup>
  import draggable from 'src/migrator/docs/vuedraggable'
  import { ref } from 'vue'

  const myArray = ref([
    { id: 1, name: 'John' },
    { id: 2, name: 'Joao' },
    { id: 3, name: 'Jean' }
  ])
  const drag = ref(false)
</script>
```

**关键点**:
- `v-for` 被 `<template #item="{element}">` 替代。
- `element` 是插槽暴露出的当前项数据。
- 必须提供 `item-key` 属性，其值对应于数组中对象的唯一键。

### 3. 与 `transition-group` 的集成

如果你使用了 `<transition-group>` 来实现拖动动画，迁移方式如下：

**Vue 2 with `vuedraggable`:**
```html
<draggable v-model="myArray">
  <transition-group name="fade">
    <div v-for="element in myArray" :key="element.id">
      {{ element.name }}
    </div>
  </transition-group>
</draggable>
```

**Vue 3 with `vue-draggable-next`:**
```vue
<template>
  <draggable
    v-model="myArray"
    tag="transition-group"
    :component-data="{ name: 'fade' }"
    item-key="id">
    <template #item="{element}">
      <div>{{element.name}}</div>
    </template>
  </draggable>
</template>

<style>
.fade-move,
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scaleY(0.01) translate(30px, 0);
}
.fade-leave-active {
  position: absolute;
}
</style>
```
**关键点**:
- 不再需要嵌套 `<transition-group>` 组件。
- 在 `<draggable>` 上使用 `tag="transition-group"`。
- 使用 `:component-data` 属性来传递给 `<transition-group>` 的 props，例如动画的 `name`。

## 属性和事件

`vue-draggable-next` 继承了 `Sortable.js` 的所有选项作为其属性，例如 `group`, `handle`, `ghost-class`, `sort` 等。事件（如 `start`, `end`, `add`, `remove`, `change`）的用法保持不变。

### Vuex 集成
与 Vuex（或 Pinia）的集成依然简单，通过计算属性的 `get` 和 `set` 即可实现。

```javascript
computed: {
  myList: {
    get() {
      return this.$store.state.myList
    },
    set(value) {
      this.$store.commit('updateList', value)
    }
  }
}
```
然后在模板中：`<draggable v-model="myList" ... >`

## 总结

迁移到 `vue-draggable-next` 需要开发者适应新的基于插槽的 API。虽然这需要修改模板代码，但它带来了更清晰的结构和对 Vue 3 渲染机制更好的兼容性。确保为每个列表项提供 `item-key` 是成功迁移的关键。

更多高级用法和示例，请查阅 [官方文档](https://github.com/SortableJS/vue-draggable-next)。
