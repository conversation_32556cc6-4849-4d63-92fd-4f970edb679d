const { execSync } = require('child_process')
const fs = require('fs-extra')
const path = require('path')
const chalk = require('chalk')
const semver = require('semver')

/**
 * Vue 3 兼容性检查器
 */
class DependencyChecker {
	constructor (projectPath) {
		this.projectPath = projectPath
		this.packageJsonPath = path.join(projectPath, 'package.json')
		this.configPath = path.join(__dirname, '../../config/package-recommend.json')
		this.config = null
		this.isVue3Project = false // 添加 Vue 3 项目标识
	}

	/**
	 * 加载配置文件
	 */
	async loadConfig() {
		try {
			this.config = await fs.readJson(this.configPath)
			// 检查项目是否已经是 Vue 3 项目
			await this.detectVueVersion()
		} catch (error) {
			console.error(chalk.red('❌ 无法加载配置文件:'), error.message)
			throw new Error(`Failed to load config file: ${this.configPath}`)
		}
	}

	/**
	 * 检测项目的 Vue 版本
	 */
	async detectVueVersion() {
		try {
			const packageJson = await fs.readJson(this.packageJsonPath)
			const allDeps = {
				...packageJson.dependencies,
				...packageJson.devDependencies
			}

			const vueVersion = allDeps.vue
			if (vueVersion) {
				// 检查是否为 Vue 3 版本
				this.isVue3Project = vueVersion.startsWith('3.') || vueVersion.startsWith('^3.') || vueVersion.startsWith('~3.')
				
				if (this.isVue3Project) {
					console.log(chalk.yellow('⚠️  检测到目标项目已经是 Vue 3 项目'))
				}
			}
		} catch (error) {
			console.warn(chalk.yellow('⚠️  无法检测 Vue 版本，将按 Vue 2 项目处理'))
		}
	}

	/**
	 * 检查所有依赖的 Vue 3 兼容性
	 */
	async checkCompatibility () {
		try {
			console.log(chalk.blue('🔍 开始检查依赖的 Vue 3 兼容性...'))

			// 加载配置文件
			await this.loadConfig()

			const packageJson = await fs.readJson(this.packageJsonPath)
			const allDeps = {
				...packageJson.dependencies,
				...packageJson.devDependencies
			}

			const results = await this.checkDependencies(allDeps)
			this.printCompatibilityReport(results)

			return results
		} catch (error) {
			console.error(chalk.red('❌ 依赖兼容性检查失败:'), error.message)
			throw error
		}
	}

	/**
	 * 检查依赖列表（使用本地映射表，快速检查）
	 */
	async checkDependencies (dependencies) {
		const results = {
			compatible: [],
			incompatible: [],
			unknown: [],
			total: Object.keys(dependencies).length
		}

		console.log(chalk.gray(`正在检查 ${results.total} 个依赖...`))

		for (const [depName, version] of Object.entries(dependencies)) {
			const compatibility = this.checkSingleDependencyLocal(depName, version)
			results[compatibility.status].push(compatibility)

			// 显示进度
			process.stdout.write('.')
		}

		console.log('\n')
		return results
	}

	/**
	 * 检查单个依赖的兼容性（本地快速检查）
	 */
	checkSingleDependencyLocal (depName, version) {
		// 跳过一些明显的系统依赖
		if (this.isSystemDependency(depName)) {
			return {
				name: depName,
				version,
				status: 'compatible',
				reason: 'System dependency'
			}
		}

		// 检查已知的不兼容包
		if (this.config.knownIncompatible[depName]) {
			const incompatibleInfo = this.config.knownIncompatible[depName]
			return {
				name: depName,
				version,
				status: 'incompatible',
				reason: incompatibleInfo.description || 'Known incompatible with Vue 3',
				alternatives: incompatibleInfo.alternatives
			}
		}

		// 检查已知的兼容包
		if (this.config.knownCompatible[depName]) {
			const compatibleInfo = this.config.knownCompatible[depName]
			return {
				name: depName,
				version,
				status: 'compatible',
				reason: compatibleInfo.description || 'Known compatible with Vue 3',
				recommendedVersion: compatibleInfo.version
			}
		}

		// 检查可能需要升级的包
		if (this.config.needsUpgrade[depName]) {
			const upgradeInfo = this.config.needsUpgrade[depName]
			return {
				name: depName,
				version,
				status: 'compatible',
				reason: upgradeInfo.description || 'Compatible with upgrade',
				recommendedVersion: upgradeInfo.version,
				note: upgradeInfo.note
			}
		}

		// 默认认为是兼容的（非 Vue 相关包）
		return {
			name: depName,
			version,
			status: 'compatible',
			reason: 'Non-Vue specific package, likely compatible'
		}
	}

	/**
	 * 获取包信息
	 */
	async getPackageInfo (packageName) {
		try {
			const result = execSync(`npm view ${packageName} --json`, {
				encoding: 'utf8',
				timeout: 10000
			})

			const packageInfo = JSON.parse(result)

			return {
				name: packageName,
				latestVersion: packageInfo.version,
				peerDependencies: packageInfo.peerDependencies || {},
				keywords: packageInfo.keywords || [],
				description: packageInfo.description || ''
			}
		} catch (error) {
			throw new Error(`Failed to get package info for ${packageName}: ${error.message}`)
		}
	}

	/**
	 * 检查 Vue 3 支持
	 */
	checkVue3Support (packageInfo) {
		const { name, peerDependencies, keywords, latestVersion } = packageInfo

		// 检查已知的不兼容包
		if (this.config.knownIncompatible[name]) {
			const incompatibleInfo = this.config.knownIncompatible[name]
			return {
				isCompatible: false,
				reason: incompatibleInfo.description || 'Known incompatible with Vue 3',
				alternatives: incompatibleInfo.alternatives
			}
		}

		// 检查已知的兼容包
		if (this.config.knownCompatible[name]) {
			const compatibleInfo = this.config.knownCompatible[name]
			return {
				isCompatible: true,
				reason: compatibleInfo.description || 'Known compatible with Vue 3',
				recommendedVersion: compatibleInfo.version || latestVersion
			}
		}

		// 检查 peerDependencies 中的 Vue 版本
		if (peerDependencies.vue) {
			const vueRange = peerDependencies.vue
			if (semver.intersects(vueRange, '>=3.0.0')) {
				return {
					isCompatible: true,
					reason: `Supports Vue 3 (peerDependencies: ${vueRange})`,
					recommendedVersion: latestVersion
				}
			} else if (semver.intersects(vueRange, '<3.0.0')) {
				return {
					isCompatible: false,
					reason: `Only supports Vue 2 (peerDependencies: ${vueRange})`
				}
			}
		}

		// 基于关键词判断
		const vueRelated = keywords.some(keyword =>
			keyword.includes('vue') || keyword.includes('element')
		)

		if (vueRelated) {
			return {
				isCompatible: false,
				reason: 'Vue-related package without Vue 3 support indication'
			}
		}

		// 默认认为是兼容的（非 Vue 相关包）
		return {
			isCompatible: true,
			reason: 'Non-Vue specific package, likely compatible',
			recommendedVersion: latestVersion
		}
	}

	/**
	 * 获取已知不兼容的包列表
	 */
	getKnownIncompatiblePackages () {
    // 为了测试，手动添加一些已知不兼容的包
    const testIncompatible = {
      'vue-datasource': {
        description: '不兼容 Vue 3，需要使用替代库或自定义组件',
        alternatives: ['vue3-table-lite', '@vueuse/core/table']
      },
      'vue-schart': {
        description: '不兼容 Vue 3，需要升级或替换',
        alternatives: ['vue-echarts', 'vue-chartjs']
      }
    };
    
    // 合并配置中的不兼容包和测试用的不兼容包
    return {
      ...this.config.knownIncompatible,
      ...testIncompatible
    };
  }

	/**
	 * 判断是否为系统依赖
	 */
	isSystemDependency (depName) {
		return this.config.systemDependencies.some(sysDep =>
			depName === sysDep || depName.startsWith(sysDep + '-') || depName.includes(sysDep)
		)
	}

	/**
	 * 打印兼容性报告
	 */
	printCompatibilityReport (results) {
		console.log('\n' + chalk.bold('📊 Vue 3 兼容性检查报告:'))
		console.log(`总计: ${results.total} 个依赖\n`)

		// 兼容的依赖
		if (results.compatible.length > 0) {
			console.log(chalk.green(`✅ 兼容 Vue 3 (${results.compatible.length}个):`))
			results.compatible.forEach(dep => {
				console.log(`  ${dep.name} - ${dep.reason}`)
			})
			console.log('')
		}

		// 不兼容的依赖
		if (results.incompatible.length > 0) {
			console.log(chalk.red(`❌ 不兼容 Vue 3 (${results.incompatible.length}个):`))
			results.incompatible.forEach(dep => {
				console.log(`  ${dep.name} - ${dep.reason}`)
				if (dep.alternatives) {
					console.log(`    建议替换为: ${dep.alternatives.join(', ')}`)
				}
			})
			console.log('')
		}

		// 未知状态的依赖
		if (results.unknown.length > 0) {
			console.log(chalk.yellow(`❓ 无法确定 (${results.unknown.length}个):`))
			results.unknown.forEach(dep => {
				console.log(`  ${dep.name} - 需要手动检查`)
			})
			console.log('')
		}

		// 总结
		const compatibilityRate = ((results.compatible.length / results.total) * 100).toFixed(1)
		console.log(chalk.bold(`兼容性: ${compatibilityRate}%`))

		if (results.incompatible.length > 0) {
			console.log(chalk.yellow('\n⚠️  建议在继续迁移前先解决不兼容的依赖问题。'))
		}
	}
}

module.exports = DependencyChecker
