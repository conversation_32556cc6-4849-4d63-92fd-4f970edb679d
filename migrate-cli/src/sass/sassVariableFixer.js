const fs = require('fs-extra');
const path = require('path');
const glob = require('glob');
const chalk = require('chalk');

class SassVariableFixer {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      include: ['**/*.scss', '**/*.sass'],
      exclude: ['node_modules/**', 'dist/**', 'build/**'],
      ...options,
    };
  }

  async fix() {
    console.log(chalk.blue('🔧 开始修复 Sass 变量引用...'));
    const sassFiles = await this.findSassFiles();

    for (const filePath of sassFiles) {
      await this.processFile(filePath, sassFiles);
    }
    console.log(chalk.green('✅ Sass 变量引用修复完成。'));
  }

  async findSassFiles() {
    const allFiles = [];
    for (const pattern of this.options.include) {
      const files = glob.sync(pattern, {
        cwd: this.projectPath,
        absolute: true,
        ignore: this.options.exclude,
      });
      allFiles.push(...files);
    }
    return [...new Set(allFiles)];
  }

  async processFile(filePath, allSassFiles) {
    let content = await fs.readFile(filePath, 'utf8');
    const usedModules = this.getUsedModules(content);

    if (usedModules.length === 0) {
      return;
    }

    let changed = false;
    for (const mod of usedModules) {
      const referencedVariables = await this.getVariablesFromModule(mod, allSassFiles);
      
      for (const variable of referencedVariables) {
        const findVarRegex = new RegExp(`(?<![a-zA-Z0-9_-]+\\.)\\$(${variable})\\b`, 'g');
        if (findVarRegex.test(content)) {
          content = content.replace(findVarRegex, `${mod.namespace}.$1`);
          changed = true;
        }
      }
    }

    if (changed) {
      await fs.writeFile(filePath, content, 'utf8');
      console.log(chalk.green(`  => 已修复: ${path.relative(this.projectPath, filePath)}`));
    }
  }

  getUsedModules(content) {
    const useRegex = /@use\s+['"]([^'"]+)['"](?:_as_([a-zA-Z0-9_-]+))?;/g;
    const modules = [];
    let match;
    while ((match = useRegex.exec(content)) !== null) {
      const modulePath = match[1];
      const namespace = match[2] || path.basename(modulePath, '.scss');
      modules.push({ path: modulePath, namespace });
    }
    return modules;
  }

  async getVariablesFromModule(mod, allSassFiles) {
    const moduleFileName = path.basename(mod.path, '.scss') + '.scss';
    const moduleFullPath = allSassFiles.find(f => f.endsWith(moduleFileName));

    if (!moduleFullPath) {
      return [];
    }

    const content = await fs.readFile(moduleFullPath, 'utf8');
    const varRegex = /\$([a-zA-Z0-9_-]+)\s*:/g;
    const variables = new Set();
    let match;
    while ((match = varRegex.exec(content)) !== null) {
      variables.add(match[1]);
    }
    return Array.from(variables);
  }
}

module.exports = SassVariableFixer; 