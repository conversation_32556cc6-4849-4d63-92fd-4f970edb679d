const path = require('path');
const fs = require('fs-extra');
const SassErrorDiagnostic = require('../../src/sass/errorDiagnostic');

describe('SassErrorDiagnostic', () => {
  let tempDir;
  let diagnostic;

  beforeEach(async () => {
    tempDir = await global.testUtils.createTempDir('error-diagnostic-');
    diagnostic = new SassErrorDiagnostic(tempDir, { verbose: false, autoFix: false });
  });

  describe('初始化', () => {
    test('应该正确初始化错误诊断器', () => {
      expect(diagnostic.projectPath).toBe(tempDir);
      expect(diagnostic.errorPatterns).toBeDefined();
      expect(diagnostic.diagnosticResults).toEqual([]);
      expect(diagnostic.fixSuggestions).toEqual([]);
    });

    test('应该包含预定义的错误模式', () => {
      const patterns = diagnostic.errorPatterns;
      
      expect(patterns.some(p => p.id === 'undefined-variable')).toBe(true);
      expect(patterns.some(p => p.id === 'module-loop')).toBe(true);
      expect(patterns.some(p => p.id === 'file-not-found')).toBe(true);
      expect(patterns.some(p => p.id === 'invalid-import')).toBe(true);
    });
  });

  describe('错误输出分析', () => {
    test('应该识别未定义变量错误', async () => {
      const errorOutput = 'Error: Undefined variable: $primary-color\n  on line 5 of src/styles/main.scss';
      
      await diagnostic.analyzeErrorOutput(errorOutput, 'src/styles/main.scss');
      
      expect(diagnostic.diagnosticResults.length).toBe(1);
      expect(diagnostic.diagnosticResults[0].id).toBe('undefined-variable');
      expect(diagnostic.diagnosticResults[0].match[1]).toBe('primary-color');
    });

    test('应该识别模块循环错误', async () => {
      const errorOutput = 'Error: Module loop: this module is already being loaded';
      
      await diagnostic.analyzeErrorOutput(errorOutput, 'src/styles/circular.scss');
      
      expect(diagnostic.diagnosticResults.length).toBe(1);
      expect(diagnostic.diagnosticResults[0].id).toBe('module-loop');
    });

    test('应该识别文件未找到错误', async () => {
      const errorOutput = 'Error: Could not find Sass file at: ~element-ui/packages/theme-chalk/src/index';
      
      await diagnostic.analyzeErrorOutput(errorOutput, 'src/styles/main.scss');
      
      expect(diagnostic.diagnosticResults.length).toBe(1);
      expect(diagnostic.diagnosticResults[0].id).toBe('file-not-found');
    });

    test('应该识别废弃导入警告', async () => {
      const errorOutput = 'Warning: @import rules are deprecated and will be removed in Dart Sass 3.0.0';
      
      await diagnostic.analyzeErrorOutput(errorOutput, 'src/styles/legacy.scss');
      
      expect(diagnostic.diagnosticResults.length).toBe(1);
      expect(diagnostic.diagnosticResults[0].id).toBe('invalid-import');
    });
  });

  describe('项目文件扫描', () => {
    test('应该扫描所有 Sass 文件', async () => {
      await global.testUtils.createTestProject(tempDir);
      
      await diagnostic.scanProjectFiles();
      
      expect(diagnostic.diagnosticResults.length).toBeGreaterThan(0);
    });

    test('应该排除指定目录', async () => {
      await fs.ensureDir(path.join(tempDir, 'node_modules'));
      await fs.writeFile(path.join(tempDir, 'node_modules/test.scss'), '@import "invalid";');
      
      await diagnostic.scanProjectFiles();
      
      const nodeModulesErrors = diagnostic.diagnosticResults.filter(d => 
        d.file && d.file.includes('node_modules')
      );
      expect(nodeModulesErrors.length).toBe(0);
    });
  });

  describe('废弃导入检查', () => {
    test('应该检测 @import 语句', () => {
      const content = `
        @import "variables";
        @import "./mixins";
        @import "~bootstrap/scss/bootstrap";
      `;
      
      diagnostic.checkDeprecatedImports(content, 'test.scss');
      
      expect(diagnostic.diagnosticResults.length).toBe(3);
      diagnostic.diagnosticResults.forEach(result => {
        expect(result.id).toBe('deprecated-import');
        expect(result.severity).toBe('warning');
      });
    });

    test('应该忽略 @use 语句', () => {
      const content = `
        @use "variables" as *;
        @use "./mixins" as mix;
      `;
      
      diagnostic.checkDeprecatedImports(content, 'test.scss');
      
      expect(diagnostic.diagnosticResults.length).toBe(0);
    });
  });

  describe('循环依赖检查', () => {
    test('应该检测自引用', () => {
      const content = '@import "./self-reference";';
      
      diagnostic.checkPotentialLoops(content, 'self-reference.scss');
      
      expect(diagnostic.diagnosticResults.length).toBe(1);
      expect(diagnostic.diagnosticResults[0].id).toBe('self-reference');
    });

    test('应该忽略正常的导入', () => {
      const content = '@import "./variables";';
      
      diagnostic.checkPotentialLoops(content, 'main.scss');
      
      expect(diagnostic.diagnosticResults.length).toBe(0);
    });
  });

  describe('路径问题检查', () => {
    test('应该检测 ~ 别名使用', () => {
      const content = `
        @import "~element-ui/packages/theme-chalk/src/index";
        @use "~bootstrap/scss/bootstrap" as *;
      `;
      
      diagnostic.checkPathIssues(content, 'test.scss');
      
      expect(diagnostic.diagnosticResults.length).toBe(2);
      diagnostic.diagnosticResults.forEach(result => {
        expect(result.id).toBe('tilde-alias');
        expect(result.severity).toBe('warning');
      });
    });
  });

  describe('变量使用检查', () => {
    test('应该检测未命名空间的变量', () => {
      const content = `
        @use "variables" as vars;
        
        .button {
          color: $primary-color; // 应该是 vars.$primary-color
          font-size: vars.$font-size;
        }
      `;
      
      diagnostic.checkVariableUsage(content, 'test.scss');
      
      expect(diagnostic.diagnosticResults.length).toBe(1);
      expect(diagnostic.diagnosticResults[0].id).toBe('unnamespaced-variable');
    });

    test('应该忽略正确命名空间的变量', () => {
      const content = `
        @use "variables" as vars;
        
        .button {
          color: vars.$primary-color;
          font-size: vars.$font-size;
        }
      `;
      
      diagnostic.checkVariableUsage(content, 'test.scss');
      
      expect(diagnostic.diagnosticResults.length).toBe(0);
    });

    test('应该忽略没有 @use 的文件', () => {
      const content = `
        .button {
          color: $primary-color;
        }
      `;
      
      diagnostic.checkVariableUsage(content, 'test.scss');
      
      expect(diagnostic.diagnosticResults.length).toBe(0);
    });
  });

  describe('修复建议生成', () => {
    test('应该为未定义变量生成修复建议', async () => {
      diagnostic.diagnosticResults.push({
        id: 'undefined-variable',
        match: ['$primary-color', 'primary-color'],
        file: 'test.scss'
      });
      
      await diagnostic.generateFixSuggestions();
      
      expect(diagnostic.fixSuggestions.length).toBe(1);
      expect(diagnostic.fixSuggestions[0].title).toContain('修复未定义变量');
      expect(diagnostic.fixSuggestions[0].actions.length).toBeGreaterThan(0);
    });

    test('应该为模块循环生成修复建议', async () => {
      diagnostic.diagnosticResults.push({
        id: 'module-loop',
        file: 'circular.scss'
      });
      
      await diagnostic.generateFixSuggestions();
      
      expect(diagnostic.fixSuggestions.length).toBe(1);
      expect(diagnostic.fixSuggestions[0].title).toBe('修复循环依赖');
      expect(diagnostic.fixSuggestions[0].autoFixable).toBe(true);
    });

    test('应该为废弃导入生成修复建议', async () => {
      diagnostic.diagnosticResults.push({
        id: 'deprecated-import',
        match: ['@import "variables";', 'variables'],
        file: 'test.scss'
      });
      
      await diagnostic.generateFixSuggestions();
      
      expect(diagnostic.fixSuggestions.length).toBe(1);
      expect(diagnostic.fixSuggestions[0].title).toBe('转换 @import 为 @use');
      expect(diagnostic.fixSuggestions[0].autoFixable).toBe(true);
    });
  });

  describe('自动修复', () => {
    beforeEach(() => {
      diagnostic.options.autoFix = true;
    });

    test('应该自动修复自引用', async () => {
      const content = `
        @import "./self-reference";
        $color: red;
      `;
      
      const testFile = path.join(tempDir, 'self-reference.scss');
      await fs.writeFile(testFile, content);
      
      await diagnostic.removeSelfReference('self-reference.scss');
      
      const updatedContent = await fs.readFile(testFile, 'utf8');
      expect(updatedContent).not.toContain('@import "./self-reference"');
      expect(updatedContent).toContain('$color: red;');
    });

    test('应该自动转换 @import 为 @use', async () => {
      const content = `
        @import "variables";
        .button { color: red; }
      `;
      
      const testFile = path.join(tempDir, 'test.scss');
      await fs.writeFile(testFile, content);
      
      await diagnostic.convertImportToUse('test.scss', '@import "variables";');
      
      const updatedContent = await fs.readFile(testFile, 'utf8');
      expect(updatedContent).toContain('@use \'variables\' as *;');
      expect(updatedContent).not.toContain('@import "variables";');
    });
  });

  describe('行号获取', () => {
    test('应该正确计算行号', () => {
      const content = `line 1
line 2
line 3
target line`;
      
      const targetIndex = content.indexOf('target');
      const lineNumber = diagnostic.getLineNumber(content, targetIndex);
      
      expect(lineNumber).toBe(4);
    });

    test('应该处理第一行', () => {
      const content = 'first line\nsecond line';
      const lineNumber = diagnostic.getLineNumber(content, 0);
      
      expect(lineNumber).toBe(1);
    });
  });

  describe('完整诊断流程', () => {
    test('应该执行完整的诊断流程', async () => {
      await global.testUtils.createTestProject(tempDir);
      
      const errorOutput = global.testUtils.createMockErrorOutput('undefined-variable');
      const report = await diagnostic.diagnose(errorOutput);
      
      expect(report.totalIssues).toBeGreaterThan(0);
      expect(report.diagnostics).toBeDefined();
      expect(report.suggestions).toBeDefined();
    });

    test('应该生成诊断报告', async () => {
      diagnostic.diagnosticResults.push({
        id: 'test-error',
        severity: 'error',
        description: 'Test error'
      });
      
      diagnostic.fixSuggestions.push({
        autoFixable: true,
        title: 'Test fix'
      });
      
      const report = diagnostic.generateDiagnosticReport();
      
      expect(report.totalIssues).toBe(1);
      expect(report.errors).toBe(1);
      expect(report.warnings).toBe(0);
      expect(report.fixableSuggestions).toBe(1);
    });
  });

  describe('错误处理', () => {
    test('应该处理文件读取错误', async () => {
      const nonExistentFile = path.join(tempDir, 'non-existent.scss');
      
      await expect(diagnostic.analyzeFile(nonExistentFile)).resolves.not.toThrow();
    });

    test('应该处理无效的正则表达式匹配', async () => {
      const invalidErrorOutput = 'Some random error message that does not match any pattern';
      
      await expect(diagnostic.analyzeErrorOutput(invalidErrorOutput)).resolves.not.toThrow();
      expect(diagnostic.diagnosticResults.length).toBe(0);
    });
  });

  describe('诊断结果打印', () => {
    test('应该打印诊断结果', () => {
      const report = {
        totalIssues: 2,
        errors: 1,
        warnings: 1,
        fixableSuggestions: 1,
        diagnostics: [
          {
            severity: 'error',
            description: 'Test error',
            file: 'test.scss',
            message: 'Error message'
          }
        ],
        suggestions: [
          {
            title: 'Test suggestion',
            description: 'Test description',
            actions: [
              { description: 'Test action', autoFixable: true }
            ]
          }
        ]
      };
      
      // 这个测试主要确保打印函数不会抛出错误
      expect(() => diagnostic.printDiagnosticResults(report)).not.toThrow();
    });
  });
});
