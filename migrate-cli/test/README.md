# 测试文档

本文档描述了 SASS 迁移自动化解决方案的完整测试套件。

## 测试架构

### 测试类型

1. **单元测试** (`test/**/*.test.js`)
   - 测试单个模块的功能
   - 快速执行，高覆盖率
   - 使用模拟数据和依赖

2. **集成测试** (`test/sass/*.test.js`)
   - 测试 Sass 迁移模块之间的协作
   - 验证模块接口和数据流
   - 使用真实的文件系统操作

3. **端到端测试** (`test/e2e/*.test.js`)
   - 测试完整的迁移流程
   - 模拟真实的项目场景
   - 验证最终用户体验

### 测试工具

- **Jest**: 测试框架和断言库
- **TestUtils**: 自定义测试工具类
- **fs-extra**: 文件系统操作
- **临时目录**: 隔离的测试环境

## 运行测试

### 快速开始

```bash
# 运行所有测试
npm test

# 运行特定类型的测试
npm run test:unit        # 单元测试
npm run test:integration # 集成测试
npm run test:e2e         # 端到端测试
npm run test:sass        # Sass 相关测试

# 生成覆盖率报告
npm run test:coverage

# 监听模式
npm run test:watch
```

### 使用测试运行器

```bash
# 使用自定义测试运行器
node scripts/run-tests.js unit
node scripts/run-tests.js sass --watch
node scripts/run-tests.js all --verbose
node scripts/run-tests.js coverage
```

## 测试模块详解

### 1. 路径解析器测试 (`pathResolver.test.js`)

测试智能路径解析器的各种场景：

- ✅ ~ 别名解析
- ✅ 相对路径处理
- ✅ Vite 配置集成
- ✅ 库迁移映射
- ✅ @import 到 @use 转换
- ✅ 命名空间生成

**关键测试用例:**
```javascript
test('应该正确解析 ~ 别名', () => {
  const result = resolver.resolveTildeAlias('~element-ui/packages/theme-chalk/src/index');
  expect(result).toBe(path.join(nodeModulesPath, 'element-ui/packages/theme-chalk/src/index'));
});
```

### 2. 架构重构引擎测试 (`architectureRefactor.test.js`)

测试项目架构重构功能：

- ✅ 依赖关系分析
- ✅ 循环依赖检测
- ✅ 变量、混入、函数提取
- ✅ 桶文件生成
- ✅ 文件结构重组

**关键测试用例:**
```javascript
test('应该检测循环依赖', async () => {
  await refactor.analyzeCurrentStructure();
  await refactor.detectCircularDependencies();
  expect(refactor.circularDependencies.length).toBeGreaterThan(0);
});
```

### 3. Element Plus 迁移器测试 (`elementPlusMigrator.test.js`)

测试 Element UI 到 Element Plus 的迁移：

- ✅ Element UI 使用检测
- ✅ 主题配置分析
- ✅ 路径映射转换
- ✅ 变量名更新
- ✅ 配置文件生成

**关键测试用例:**
```javascript
test('应该生成 Element Plus 配置文件', async () => {
  await migrator.generateElementPlusConfig(theme);
  const configPath = path.join(tempDir, 'src/styles/element/index.scss');
  expect(await fs.pathExists(configPath)).toBe(true);
});
```

### 4. 错误诊断系统测试 (`errorDiagnostic.test.js`)

测试智能错误诊断和修复：

- ✅ 错误模式识别
- ✅ 修复建议生成
- ✅ 自动修复功能
- ✅ 诊断报告生成

**关键测试用例:**
```javascript
test('应该识别未定义变量错误', async () => {
  const errorOutput = 'Error: Undefined variable: $primary-color';
  await diagnostic.analyzeErrorOutput(errorOutput);
  expect(diagnostic.diagnosticResults[0].id).toBe('undefined-variable');
});
```

### 5. Vite 配置优化器测试 (`viteConfigOptimizer.test.js`)

测试 Vite 配置的自动优化：

- ✅ 配置文件解析
- ✅ 优化建议生成
- ✅ 配置自动应用
- ✅ 验证和错误检测

**关键测试用例:**
```javascript
test('应该添加 loadPaths 配置', () => {
  const result = optimizer.addLoadPaths(content);
  expect(result).toContain('loadPaths');
  expect(result).toContain('node_modules');
});
```

### 6. 增强版迁移器集成测试 (`enhancedSassMigrator.test.js`)

测试完整的迁移流程：

- ✅ 多阶段迁移流程
- ✅ 模块间协作
- ✅ 错误处理
- ✅ 统计和报告

**关键测试用例:**
```javascript
test('应该执行完整的迁移流程', async () => {
  const report = await migrator.migrate();
  expect(report.summary.totalFiles).toBeGreaterThan(0);
  expect(report.optimizations).toBeDefined();
});
```

### 7. 端到端测试 (`migration.e2e.test.js`)

测试真实项目场景：

- ✅ 简单 Vue 项目迁移
- ✅ Element UI 项目迁移
- ✅ 复杂项目结构处理
- ✅ CLI 选项验证
- ✅ 性能测试

**关键测试用例:**
```javascript
test('应该成功迁移简单的 Vue 项目', async () => {
  const result = await runMigrationCLI(tempDir, ['--dry-run', '--enhanced']);
  expect(result.exitCode).toBe(0);
  expect(result.stdout).toContain('Sass 迁移完成');
});
```

## 测试数据和工具

### TestUtils 类

提供测试辅助功能：

```javascript
// 创建临时测试目录
const tempDir = await testUtils.createTempDir();

// 创建测试项目
await testUtils.createTestProject(tempDir);
await testUtils.createElementUIProject(tempDir);
await testUtils.createCircularDependencyProject(tempDir);

// 验证迁移结果
const validation = await testUtils.validateMigrationResult(tempDir);
```

### 模拟数据

- **项目结构**: 各种类型的 Vue 项目模板
- **配置文件**: Vite、package.json 等配置
- **Sass 文件**: 包含不同语法和依赖的样式文件
- **错误输出**: 各种类型的编译错误信息

## 覆盖率目标

当前覆盖率目标：

- **分支覆盖率**: ≥ 75%
- **函数覆盖率**: ≥ 75%
- **行覆盖率**: ≥ 75%
- **语句覆盖率**: ≥ 75%

## 持续集成

### GitHub Actions 配置

```yaml
- name: Run tests
  run: |
    npm run test:ci
    npm run lint
```

### 测试策略

1. **快速反馈**: 单元测试优先
2. **并行执行**: 独立测试并行运行
3. **隔离环境**: 每个测试使用独立的临时目录
4. **清理机制**: 自动清理测试产生的临时文件

## 调试测试

### 常见问题

1. **测试超时**: 增加 Jest 超时时间
2. **文件权限**: 确保测试目录有写权限
3. **端口冲突**: 端到端测试可能需要特定端口

### 调试技巧

```bash
# 运行单个测试文件
npx jest test/sass/pathResolver.test.js

# 启用详细输出
npx jest --verbose

# 调试模式
node --inspect-brk node_modules/.bin/jest --runInBand
```

## 贡献指南

### 添加新测试

1. 在相应目录创建 `.test.js` 文件
2. 使用 `describe` 和 `test` 组织测试
3. 使用 `TestUtils` 创建测试环境
4. 确保测试独立且可重复

### 测试命名规范

- 描述性的测试名称
- 使用 "应该..." 格式
- 明确测试的预期行为

### 最佳实践

- 每个测试应该独立
- 使用适当的断言
- 清理测试资源
- 保持测试简洁明了
