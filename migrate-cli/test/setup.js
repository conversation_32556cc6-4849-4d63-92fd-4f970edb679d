/**
 * Jest 测试环境设置
 */

// 设置测试超时时间
jest.setTimeout(30000);

// 模拟 console 方法以减少测试输出噪音
const originalConsole = global.console;

beforeAll(() => {
  global.console = {
    ...originalConsole,
    log: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  };
});

afterAll(() => {
  global.console = originalConsole;
});

// 全局测试工具
const TestUtils = require('./helpers/testUtils');
global.testUtils = new TestUtils();

// 全局清理
afterEach(async () => {
  await global.testUtils.cleanup();
  jest.clearAllMocks();
});

// 模拟 chalk 以避免颜色代码干扰测试
jest.mock('chalk', () => ({
  red: jest.fn(text => text),
  green: jest.fn(text => text),
  blue: jest.fn(text => text),
  yellow: jest.fn(text => text),
  gray: jest.fn(text => text),
  bold: {
    blue: jest.fn(text => text),
    green: jest.fn(text => text),
    red: jest.fn(text => text)
  }
}));

// 模拟 ora (loading spinner)
jest.mock('ora', () => {
  return jest.fn(() => ({
    start: jest.fn().mockReturnThis(),
    succeed: jest.fn().mockReturnThis(),
    fail: jest.fn().mockReturnThis(),
    warn: jest.fn().mockReturnThis(),
    stop: jest.fn().mockReturnThis(),
    text: ''
  }));
});

// 设置环境变量
process.env.NODE_ENV = 'test';
