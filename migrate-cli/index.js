const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const ora = require('ora');

// 导入所有模块
const PackageUpgrader = require('./src/dependency/packageUpgrader');
const DependencyChecker = require('./src/dependency/dependencyChecker');
const CodeMigrator = require('./src/codeMigrator');
const FailureLogger = require('./src/failureLogger');
const AIRepairer = require('./src/aiRepairer');
const ESLintFixer = require('./src/eslintFixer');
const BuildFixer = require('./src/buildFixer');
const SassMigrator = require('./src/sassMigrator');

/**
 * Vue 2 到 Vue 3 迁移工具主类
 */
class Vue2to3Migrator {
  constructor(projectPath, options = {}) {
    this.projectPath = path.resolve(projectPath);
    this.options = {
      skipDependencyCheck: false,
      skipAIRepair: false,
      skipESLint: false,
      skipBuild: false,
      skipSassMigration: false,
      aiApiKey: process.env.OPENAI_API_KEY,
      buildCommand: 'npm run build',
      ...options
    };

    this.stats = {
      startTime: Date.now(),
      endTime: null,
      totalSteps: 8,
      completedSteps: 0,
      success: false,
      errors: []
    };

    // 初始化组件
    this.failureLogger = new FailureLogger(this.projectPath);
    this.aiRepairer = new AIRepairer({ apiKey: this.options.aiApiKey });
  }

  /**
   * 执行完整的迁移流程
   */
  async migrate() {
    try {
      console.log(chalk.bold.blue('\n🚀 Vue 2 到 Vue 3 迁移工具'));
      console.log(chalk.gray(`项目路径: ${this.projectPath}`));
      console.log(chalk.gray(`开始时间: ${new Date().toLocaleString()}\n`));

      // 验证项目
      await this.validateProject();

      // 初始化日志
      await this.failureLogger.initialize();

      // 步骤 1: 升级 package.json 依赖
      await this.step1_upgradePackageJson();

      // 步骤 2: 检查依赖兼容性（可选）
      if (!this.options.skipDependencyCheck) {
        await this.step2_checkDependencies();
      } else {
        this.skipStep('依赖兼容性检查');
      }

      // 步骤 3: 批量迁移代码文件
      await this.step3_migrateCode();

      // 步骤 4: 记录失败文件
      await this.step4_logFailures();

      // 步骤 5: AI 修复失败文件（可选）
      if (!this.options.skipAIRepair && this.aiRepairer.isEnabled()) {
        await this.step5_aiRepair();
      } else {
        this.skipStep('AI 修复');
      }

      // 步骤 6: Sass 语法迁移（可选）
      if (!this.options.skipSassMigration) {
        await this.step6_sassMigration();
      } else {
        this.skipStep('Sass 语法迁移');
      }

      // 步骤 7: ESLint 自动修复（可选）
      if (!this.options.skipESLint) {
        await this.step7_eslintFix();
      } else {
        this.skipStep('ESLint 修复');
      }

      // 步骤 8: 构建项目并修复错误（可选）
      if (!this.options.skipBuild) {
        await this.step8_buildAndFix();
      } else {
        this.skipStep('构建修复');
      }

      // 完成迁移
      await this.completeMigration();

    } catch (error) {
      await this.handleMigrationError(error);
      throw error;
    }
  }

  /**
   * 验证项目
   */
  async validateProject() {
    const spinner = ora('验证项目结构...').start();

    try {
      // 检查项目目录是否存在
      if (!await fs.pathExists(this.projectPath)) {
        throw new Error(`项目目录不存在: ${this.projectPath}`);
      }

      // 检查 package.json
      const packageJsonPath = path.join(this.projectPath, 'package.json');
      if (!await fs.pathExists(packageJsonPath)) {
        throw new Error('未找到 package.json 文件');
      }

      // 检查是否为 Vue 项目
      const packageJson = await fs.readJson(packageJsonPath);
      if (!packageJson.dependencies?.vue && !packageJson.devDependencies?.vue) {
        throw new Error('这不是一个 Vue 项目');
      }

      // 检查 Vue 版本
      const vueVersion = packageJson.dependencies?.vue || packageJson.devDependencies?.vue;
      if (vueVersion && !vueVersion.startsWith('2.')) {
        console.log(chalk.yellow('⚠️  检测到非 Vue 2 项目，继续迁移可能会有问题'));
      }

      spinner.succeed('项目验证通过');
    } catch (error) {
      spinner.fail('项目验证失败');
      throw error;
    }
  }

  /**
   * 步骤 1: 升级 package.json 依赖
   */
  async step1_upgradePackageJson() {
    const spinner = ora('步骤 1/7: 升级 package.json 依赖...').start();

    try {
      const upgrader = new PackageUpgrader(this.projectPath, {
        migrationMode: false,
        preserveVue3Dependencies: true
      });
      const result = await upgrader.upgrade();

      this.stats.packageUpgrade = result;
      this.completeStep();

      spinner.succeed('步骤 1/7: package.json 依赖升级完成');
    } catch (error) {
      spinner.fail('步骤 1/7: package.json 依赖升级失败');
      this.stats.errors.push({ step: 1, error: error.message });
      throw error;
    }
  }

  /**
   * 步骤 2: 检查依赖兼容性
   */
  async step2_checkDependencies() {
    const spinner = ora('步骤 2/7: 检查依赖兼容性...').start();

    try {
      const checker = new DependencyChecker(this.projectPath);
      const result = await checker.checkCompatibility();

      this.stats.dependencyCheck = result;
      this.completeStep();

      spinner.succeed('步骤 2/7: 依赖兼容性检查完成');

      // 如果有不兼容的依赖，给出警告
      if (result.incompatible.length > 0) {
        console.log(chalk.yellow(`⚠️  发现 ${result.incompatible.length} 个不兼容的依赖，建议先手动处理`));
      }
    } catch (error) {
      spinner.fail('步骤 2/7: 依赖兼容性检查失败');
      this.stats.errors.push({ step: 2, error: error.message });
      // 这个步骤失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  依赖检查失败，继续后续步骤'));
      this.completeStep();
    }
  }

  /**
   * 步骤 3: 批量迁移代码文件
   */
  async step3_migrateCode() {
    const spinner = ora('步骤 3/7: 批量迁移代码文件...').start();

    try {
      const migrator = new CodeMigrator(this.projectPath);
      const result = await migrator.migrate();

      this.stats.codeMigration = result;
      this.failedFiles = migrator.getFailedFiles();
      this.completeStep();

      spinner.succeed('步骤 3/7: 代码文件迁移完成');
    } catch (error) {
      spinner.fail('步骤 3/7: 代码文件迁移失败');
      this.stats.errors.push({ step: 3, error: error.message });
      throw error;
    }
  }

  /**
   * 步骤 4: 记录失败文件
   */
  async step4_logFailures() {
    const spinner = ora('步骤 4/7: 记录失败文件...').start();

    try {
      // 记录所有失败的文件
      if (this.failedFiles && this.failedFiles.length > 0) {
        for (const failedFile of this.failedFiles) {
          await this.failureLogger.logFailure(
            failedFile.file,
            new Error(failedFile.error),
            { step: 'code-migration' }
          );
        }
      }

      await this.failureLogger.saveFailures();
      this.completeStep();

      spinner.succeed('步骤 4/7: 失败文件记录完成');
    } catch (error) {
      spinner.fail('步骤 4/7: 失败文件记录失败');
      this.stats.errors.push({ step: 4, error: error.message });
      // 这个步骤失败不应该中断整个流程
      this.completeStep();
    }
  }

  /**
   * 步骤 5: AI 修复失败文件
   */
  async step5_aiRepair() {
    const spinner = ora('步骤 5/7: AI 修复失败文件...').start();

    try {
      const failedFiles = this.failureLogger.getFailures();

      if (failedFiles.length === 0) {
        spinner.succeed('步骤 5/7: 没有需要 AI 修复的文件');
        this.completeStep();
        return;
      }

      const result = await this.aiRepairer.repairFailedFiles(failedFiles, this.projectPath);
      this.stats.aiRepair = result;
      this.completeStep();

      spinner.succeed('步骤 5/7: AI 修复完成');
    } catch (error) {
      spinner.fail('步骤 5/7: AI 修复失败');
      this.stats.errors.push({ step: 5, error: error.message });
      // AI 修复失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  AI 修复失败，继续后续步骤'));
      this.completeStep();
    }
  }

  /**
   * 步骤 6: Sass 语法迁移
   */
  async step6_sassMigration() {
    const spinner = ora('步骤 6/8: Sass 语法迁移...').start();

    try {
      const sassMigrator = new SassMigrator(this.projectPath);

      // 检查是否有 Sass 文件需要迁移
      const sassFiles = await sassMigrator.findSassFiles();
      
      if (sassFiles.length === 0) {
        spinner.succeed('步骤 6/8: 未找到需要迁移的 Sass 文件');
        this.completeStep();
        return;
      }

      // 检查 sass-migrator 是否可用
      if (!await sassMigrator.isSassMigratorAvailable()) {
        spinner.warn('步骤 6/8: sass-migrator 不可用，跳过 Sass 迁移');
        console.log(chalk.yellow('   提示: 运行 "npm install -g sass-migrator" 安装工具'));
        this.completeStep();
        return;
      }

      const result = await sassMigrator.migrate();
      this.stats.sassMigration = result;
      this.completeStep();

      if (result.processedFiles > 0) {
        spinner.succeed('步骤 6/8: Sass 语法迁移完成');
      } else {
        spinner.succeed('步骤 6/8: 所有 Sass 文件无需迁移');
      }

    } catch (error) {
      spinner.fail('步骤 6/8: Sass 语法迁移失败');
      this.stats.errors.push({ step: 6, error: error.message });
      // Sass 迁移失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  Sass 迁移失败，继续后续步骤'));
      this.completeStep();
    }
  }

  /**
   * 步骤 7: ESLint 自动修复
   */
  async step7_eslintFix() {
    const spinner = ora('步骤 7/8: ESLint 自动修复...').start();

    try {
      const eslintFixer = new ESLintFixer(this.projectPath);

      // 检查 ESLint 是否可用
      if (!await eslintFixer.isESLintAvailable()) {
        spinner.warn('步骤 7/8: ESLint 不可用，跳过此步骤');
        this.completeStep();
        return;
      }

      const result = await eslintFixer.fix();
      this.stats.eslintFix = result;
      this.completeStep();

      spinner.succeed('步骤 7/8: ESLint 修复完成');
    } catch (error) {
      spinner.fail('步骤 7/8: ESLint 修复失败');
      this.stats.errors.push({ step: 7, error: error.message });
      // ESLint 修复失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  ESLint 修复失败，继续后续步骤'));
      this.completeStep();
    }
  }

  /**
   * 步骤 8: 构建项目并修复错误
   */
  async step8_buildAndFix() {
    const spinner = ora('步骤 8/8: 构建项目并修复错误...').start();

    try {
      const buildFixer = new BuildFixer(this.projectPath, {
        buildCommand: this.options.buildCommand,
        aiRepairer: this.aiRepairer
      });

      const result = await buildFixer.buildAndFix();
      this.stats.buildFix = result;
      this.completeStep();

      if (result.success) {
        spinner.succeed('步骤 8/8: 项目构建成功');
      } else {
        spinner.warn('步骤 8/8: 项目构建仍有问题，需要手动修复');
      }
    } catch (error) {
      spinner.fail('步骤 8/8: 构建修复失败');
      this.stats.errors.push({ step: 8, error: error.message });
      // 构建失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  构建修复失败，可能需要手动处理'));
      this.completeStep();
    }
  }

  /**
   * 完成迁移
   */
  async completeMigration() {
    this.stats.endTime = Date.now();
    this.stats.duration = this.stats.endTime - this.stats.startTime;
    this.stats.success = this.stats.errors.length === 0;

    console.log('\n' + chalk.bold.green('🎉 Vue 2 到 Vue 3 迁移完成!'));
    this.printFinalStats();

    // 生成迁移报告
    await this.generateMigrationReport();
  }

  /**
   * 处理迁移错误
   */
  async handleMigrationError(error) {
    this.stats.endTime = Date.now();
    this.stats.duration = this.stats.endTime - this.stats.startTime;
    this.stats.success = false;

    console.log('\n' + chalk.bold.red('❌ 迁移过程中发生错误'));
    console.log(chalk.red(error.message));

    await this.generateMigrationReport();
  }

  /**
   * 跳过步骤
   */
  skipStep(stepName) {
    console.log(chalk.gray(`⏭️  跳过: ${stepName}`));
    this.completeStep();
  }

  /**
   * 完成步骤
   */
  completeStep() {
    this.stats.completedSteps++;
  }

  /**
   * 打印最终统计
   */
  printFinalStats() {
    const duration = Math.round(this.stats.duration / 1000);

    console.log('\n' + chalk.bold('📊 迁移统计:'));
    console.log(`耗时: ${duration} 秒`);
    console.log(`完成步骤: ${this.stats.completedSteps}/${this.stats.totalSteps}`);
    console.log(`错误数量: ${this.stats.errors.length}`);

    if (this.stats.codeMigration) {
      console.log(`代码文件: ${this.stats.codeMigration.success} 成功, ${this.stats.codeMigration.failed} 失败`);
    }

    if (this.stats.buildFix?.success) {
      console.log(chalk.green('✅ 项目可以成功构建'));
    } else {
      console.log(chalk.yellow('⚠️  项目构建可能仍有问题'));
    }
  }

  /**
   * 生成迁移报告
   */
  async generateMigrationReport() {
    const reportPath = path.join(this.projectPath, 'migration-report.json');

    const report = {
      timestamp: new Date().toISOString(),
      projectPath: this.projectPath,
      options: this.options,
      stats: this.stats,
      success: this.stats.success,
      duration: this.stats.duration,
      recommendations: this.generateRecommendations()
    };

    await fs.writeJson(reportPath, report, { spaces: 2 });
    console.log(chalk.blue(`📄 迁移报告已生成: ${reportPath}`));
  }

  /**
   * 生成建议
   */
  generateRecommendations() {
    const recommendations = [];

    if (this.stats.errors.length > 0) {
      recommendations.push('检查错误日志并手动修复剩余问题');
    }

    if (this.stats.codeMigration?.failed > 0) {
      recommendations.push('手动检查迁移失败的文件');
    }

    if (!this.stats.buildFix?.success) {
      recommendations.push('运行构建命令检查剩余的构建错误');
    }

    recommendations.push('运行测试确保功能正常');
    recommendations.push('检查 UI 组件是否正确迁移到 Element Plus');
    recommendations.push('更新文档和部署配置');

    return recommendations;
  }
}

module.exports = Vue2to3Migrator;
