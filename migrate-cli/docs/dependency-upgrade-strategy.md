# 智能依赖升级策略

## 概述

为了解决从 Vue 2 项目迁移到 Vue 3 项目时的依赖冲突问题，我们实现了一个智能的依赖升级策略。该策略能够：

1. **检测目标项目类型**：自动识别目标项目是否已经是 Vue 3 项目
2. **保护现有依赖**：在 Vue 3 项目中保留已有的兼容依赖版本
3. **条件性添加依赖**：只在必要时添加新依赖

## 工作原理

### 1. 项目类型检测

系统会检查 `package.json` 中的 Vue 版本：

```javascript
// Vue 3 项目检测
const vueVersion = dependencies.vue
this.isVue3Project = vueVersion.startsWith('3.') || 
                     vueVersion.startsWith('^3.') || 
                     vueVersion.startsWith('~3.')
```

### 2. 依赖保护机制

当检测到 Vue 3 项目时，系统会：

- **保留兼容版本**：如果依赖已经是 Vue 3 兼容版本，则不进行升级
- **跳过不必要的依赖**：避免添加目标项目中不需要的依赖

```javascript
// 检查是否为 Vue 3 兼容版本
const vue3CompatibleVersions = {
  'vue': ['3.', '^3.', '~3.'],
  'vue-router': ['4.', '^4.', '~4.'],
  'vuex': ['4.', '^4.', '~4.'],
  'element-plus': ['2.', '^2.', '~2.'],
  'sass': ['1.', '^1.', '~1.'],
  'vite': ['4.', '^4.', '~4.', '5.', '^5.', '~5.'],
  'typescript': ['4.', '^4.', '~4.', '5.', '^5.', '~5.']
}

// 现代版本阈值检查
const modernVersionThresholds = {
  'sass': 1.30, // sass 1.30+ 支持更好的 Vue 3 集成
  'sass-loader': 12, // sass-loader 12+ 支持 webpack 5
  'webpack': 5, // webpack 5+
  'typescript': 4, // TypeScript 4+
  'eslint': 8, // ESLint 8+
  'postcss': 8, // PostCSS 8+
}
```

### 3. 条件依赖规则

系统使用配置文件中的规则来决定是否添加特定依赖：

```json
{
  "migrationSettings": {
    "conditionalDependencies": {
      "rules": {
        "element-plus": {
          "condition": "hasElementUI",
          "description": "只在项目使用 element-ui 时添加 element-plus"
        },
        "@element-plus/icons-vue": {
          "condition": "hasElementUI || hasElementPlus", 
          "description": "只在项目使用 element-ui 或 element-plus 时添加图标库"
        }
      }
    }
  }
}
```

## 使用方式

### 1. 标准迁移模式

```javascript
const upgrader = new PackageUpgrader(projectPath, {
  migrationMode: true,
  preserveVue3Dependencies: true
})
```

### 2. 强制升级模式

```javascript
const upgrader = new PackageUpgrader(projectPath, {
  migrationMode: false,
  preserveVue3Dependencies: false
})
```

## 迁移场景

### 场景 1：Vue 2 → Vue 3 新项目（现代构建工具）

```javascript
// 源项目 (Vue 2)
{
  "dependencies": {
    "vue": "^2.6.14",
    "element-ui": "^2.15.14"
  },
  "devDependencies": {
    "node-sass": "^6.0.0",
    "sass-loader": "^10.0.0"
  }
}

// 目标项目 (Vue 3 + 现代工具)
{
  "dependencies": {
    "vue": "^3.4.0",
    "element-plus": "^2.9.0"
  },
  "devDependencies": {
    "sass": "^1.70.0",
    "sass-loader": "^13.0.0",
    "vite": "^5.1.0"
  }
}

// 结果：保留 Vue 3 项目的现代工具版本，不会被降级
```

### 场景 2：旧版本现代工具升级

```javascript
// Vue 3 项目但使用旧版本工具
{
  "dependencies": {
    "vue": "^3.4.0"
  },
  "devDependencies": {
    "sass": "^1.26.0",      // 旧版本 sass
    "typescript": "^3.9.0", // 旧版本 TypeScript
    "eslint": "^7.32.0"     // 旧版本 ESLint
  }
}

// 结果：智能升级到现代版本
{
  "dependencies": {
    "vue": "^3.4.0"
  },
  "devDependencies": {
    "sass": "^1.82.0",      // 升级到最新稳定版
    "typescript": "^5.3.0", // 升级到最新稳定版
    "eslint": "^8.56.0"     // 升级到最新稳定版
  }
}
```

## 配置选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `migrationMode` | boolean | false | 是否为迁移模式 |
| `preserveVue3Dependencies` | boolean | true | 是否保留 Vue 3 依赖 |
| `verbose` | boolean | false | 是否显示详细信息 |

## 日志输出

系统会提供详细的日志信息：

```
⚠️  检测到 Vue 3 项目，将保留现有兼容依赖版本
  保留 vue@^3.4.0 (已是 Vue 3 兼容版本)
  保留 element-plus@^2.9.0 (已是 Vue 3 兼容版本)
  保留 sass@^1.70.0 (现代依赖，无需升级)
  保留 vite@^5.1.0 (现代依赖，无需升级)
  跳过添加 element-plus (Vue 3 项目中非必需)
```

## 支持的现代依赖

系统能够智能识别和保护以下类型的现代依赖：

### 构建工具
- **Sass**: `sass ^1.30+` (替代废弃的 `node-sass`)
- **Vite**: `vite ^4.0+` 或 `^5.0+`
- **Webpack**: `webpack ^5.0+`
- **TypeScript**: `typescript ^4.0+` 或 `^5.0+`

### Vue 生态系统
- **Vue 3 核心**: `vue ^3.0+`, `vue-router ^4.0+`
- **状态管理**: `vuex ^4.0+`, `pinia ^2.0+`
- **UI 库**: `element-plus ^2.0+`
- **开发工具**: `@vue/compiler-sfc ^3.0+`

### 现代包模式识别
- `@vue/*` - Vue 官方包
- `@vueuse/*` - VueUse 工具库
- `@vitejs/*` - Vite 相关
- `vue3-*` - Vue 3 专用包
- `@types/*` - TypeScript 类型定义

## 测试覆盖

- ✅ Vue 3 项目依赖保护
- ✅ Vue 2 项目正常升级
- ✅ 条件依赖规则
- ✅ 版本兼容性检查
- ✅ 现代构建工具保护 (sass, vite, typescript等)
- ✅ 废弃依赖识别 (node-sass等)
- ✅ 现代包名模式识别

这个策略确保了迁移过程的安全性和准确性，避免了依赖版本冲突和功能回退的问题，特别是对现代构建工具链的完整保护。 