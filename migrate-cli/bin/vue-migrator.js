#!/usr/bin/env node

// 加载环境变量
require('dotenv').config();

const { Command } = require('commander');
const chalk = require('chalk');
const path = require('path');
const fs = require('fs-extra');
const ora = require('ora');

// 导入所有模块
const PackageUpgrader = require('../src/dependency/packageUpgrader');
const DependencyChecker = require('../src/dependency/dependencyChecker');
const AIDependencyConverter = require('../src/dependency/aiDependencyConverter');
const CodeMigrator = require('../src/codeMigrator');
const FailureLogger = require('../src/failureLogger');
const AIRepairer = require('../src/aiRepairer');
const ESLintFixer = require('../src/eslintFixer');
const BuildFixer = require('../src/buildFixer');
const AutoMigrator = require('../src/core/autoMigrator');
const DependencyMapper = require('../src/migrator/dependencyMapper');
const DependencyCodeMigrator = require('../src/migrator/dependencyCodeMigrator');
const SassMigrator = require('../src/sassMigrator');
const SassVariableFixer = require('../src/sass/sassVariableFixer');

/**
 * 统一的 Vue 2 到 Vue 3 迁移工具
 * 整合了完整迁移和分步执行功能
 */
class UnifiedVueMigrator {
  constructor(projectPath, options = {}) {
    this.projectPath = path.resolve(projectPath);
    this.options = {
      skipDependencyCheck: options.skipDependencyCheck || false,
      skipAIRepair: options.skipAIRepair || false,
      skipESLint: options.skipESLint !== undefined ? options.skipESLint : true,
      skipBuild: options.skipBuild || false,
      skipSassMigration: options.skipSassMigration || false,
      aiApiKey: options.aiApiKey || process.env.GLM_API_KEY || process.env.DEEPSEEK_API_KEY || process.env.OPENAI_API_KEY,
      buildCommand: options.buildCommand || 'npm run build',
      dryRun: options.dryRun || false,
      verbose: options.verbose || false,
      ...options
    };

    this.stats = {
      startTime: Date.now(),
      endTime: null,
      totalSteps: 8,
      completedSteps: 0,
      success: false,
      errors: [],
      stepResults: {}
    };

    this.failedFiles = [];
    this.spinner = null;
  }

  /**
   * 执行完整的 7 步迁移流程
   */
  async migrate() {
    try {
      this.printHeader();

      // 验证项目
      await this.validateProject();

      // 执行 8 个步骤
      await this.step1_upgradePackageJson();
      await this.step2_checkDependencies();
      await this.step3_migrateCode();
      await this.step4_logFailures();
      await this.step5_aiRepair();
      await this.step6_sassMigration();
      await this.step6_5_fixSassVariables();
      await this.step7_eslintFix();
      await this.step8_buildAndFix();

      // 完成迁移
      await this.completeMigration();
    } catch (error) {
      await this.handleMigrationError(error);
      throw error;
    }
  }

  printHeader() {
    console.log(chalk.bold.blue('\n🚀 Vue 2 到 Vue 3 统一迁移工具\n'));
    console.log(chalk.gray(`项目路径: ${this.projectPath}`));
    console.log(chalk.gray(`开始时间: ${new Date().toLocaleString()}`));
    console.log(chalk.gray(`模式: ${this.options.dryRun ? '预览模式' : '实际执行'}\n`));
  }

  async validateProject() {
    this.spinner = ora('验证项目结构...').start();

    try {
      // 检查项目目录是否存在
      if (!await fs.pathExists(this.projectPath)) {
        throw new Error(`项目目录不存在: ${this.projectPath}`);
      }

      // 检查 package.json
      const packageJsonPath = path.join(this.projectPath, 'package.json');
      if (!await fs.pathExists(packageJsonPath)) {
        throw new Error('未找到 package.json 文件');
      }

      // 检查是否为 Vue 项目
      const packageJson = await fs.readJson(packageJsonPath);
      if (!packageJson.dependencies?.vue && !packageJson.devDependencies?.vue) {
        throw new Error('这不是一个 Vue 项目');
      }

      // 检查 Vue 版本
      const vueVersion = packageJson.dependencies?.vue || packageJson.devDependencies?.vue;
      if (vueVersion && vueVersion.startsWith('3.')) {
        console.log(chalk.yellow('⚠️  检测到 Vue 3 项目，可能不需要迁移'));
      }

      this.spinner.succeed('项目验证通过');
    } catch (error) {
      this.spinner.fail('项目验证失败');
      throw error;
    }
  }

  /**
   * 步骤 1: 升级 package.json 依赖
   */
  async step1_upgradePackageJson() {
    if (this.options.skipDependencyCheck && this.stats.completedSteps >= 1) {
      this.skipStep('package.json 依赖升级');
      return;
    }

    this.spinner = ora('步骤 1/8: 升级 package.json 依赖...').start();

    try {
      const upgrader = new PackageUpgrader(this.projectPath, {
        migrationMode: false,
        preserveVue3Dependencies: true
      });
      const result = await upgrader.upgrade();

      this.stats.stepResults.packageUpgrade = result;
      this.completeStep();

      this.spinner.succeed('步骤 1/8: package.json 依赖升级完成');

      if (this.options.verbose && result.changes?.length > 0) {
        console.log(chalk.gray('  变更详情:'));
        result.changes.forEach(change => console.log(chalk.gray(`    ${change}`)));
      }
    } catch (error) {
      this.spinner.fail('步骤 1/8: package.json 依赖升级失败');
      this.stats.errors.push({ step: 1, error: error.message });
      throw error;
    }
  }

  /**
   * 步骤 2: 检查依赖兼容性
   */
  async step2_checkDependencies() {
    if (this.options.skipDependencyCheck) {
      this.skipStep('依赖兼容性检查');
      return;
    }

    this.spinner = ora('检查依赖兼容性...').start();
    try {
      const checker = new DependencyChecker(this.projectPath);
      const results = await checker.checkCompatibility();

      // 如果有不兼容的依赖，尝试使用 AI 转换
      if (results.incompatible.length > 0) {
        this.spinner.warn(`发现 ${results.incompatible.length} 个不兼容的依赖`);
        console.log(chalk.yellow('⚠️ 这些依赖可能会在 Vue 3 中出现问题。'));

        // 检查是否可以使用 AI 进行转换
        if (!this.options.skipAIRepair && this.options.aiApiKey) {
          console.log(chalk.blue('🤖 尝试使用 AI 自动转换不兼容的依赖...'));

          const converter = new AIDependencyConverter({
            projectPath: this.projectPath,
            aiApiKey: this.options.aiApiKey,
            verbose: this.options.verbose
          });

          if (converter.isEnabled()) {
            const conversionResult = await converter.convertIncompatibleDependencies(results.incompatible);

            if (conversionResult.success && conversionResult.stats.success > 0) {
              console.log(chalk.green(`✅ AI 成功转换了 ${conversionResult.stats.success} 个不兼容的依赖!`));
            } else {
              console.log(chalk.yellow('⚠️ AI 无法转换所有不兼容的依赖，建议手动检查。'));
            }
          } else {
            console.log(chalk.yellow('⚠️ AI 服务未启用，跳过依赖转换。建议替换或升级这些依赖。'));
          }
        } else {
          console.log(chalk.yellow('⚠️ 建议替换或升级这些依赖，或启用 AI 修复功能自动转换。'));
        }
      } else {
        this.spinner.succeed('所有依赖已兼容 Vue 3！');
      }

      this.stats.stepResults.dependencyCheck = {
        compatible: results.compatible.length,
        incompatible: results.incompatible.length,
        unknown: results.unknown.length,
        total: results.total
      };

      this.completeStep();
    } catch (error) {
      this.spinner.fail('依赖检查失败');
      console.error(chalk.red('❌ 依赖检查错误:'), error.message);
      throw error;
    }
  }

  /**
   * 步骤 3: 批量迁移代码文件
   */
  async step3_migrateCode() {
    this.spinner = ora('步骤 3/8: 批量迁移代码文件...').start();

    try {
      const migrator = new CodeMigrator(this.projectPath);
      const result = await migrator.migrate();

      this.stats.stepResults.codeMigration = result;
      this.failedFiles = migrator.getFailedFiles();

      // 如果有失败文件，显示详细信息
      if (this.failedFiles.length > 0 && this.options.verbose) {
        console.log(chalk.yellow(`\n⚠️  发现 ${this.failedFiles.length} 个转换失败的文件:`));
        this.failedFiles.forEach(file => {
          console.log(chalk.gray(`  ${file.file} - ${file.errorType}: ${file.error.substring(0, 100)}...`));
        });
      }

      this.completeStep();

      this.spinner.succeed('步骤 3/8: 代码文件迁移完成');

      if (this.options.verbose) {
        console.log(chalk.gray(`  成功: ${result.success || 0}, 失败: ${result.failed || 0}`));
      }
    } catch (error) {
      this.spinner.fail('步骤 3/8: 代码文件迁移失败');
      this.stats.errors.push({ step: 3, error: error.message });
      throw error;
    }
  }

  /**
   * 步骤 4: 记录失败文件
   */
  async step4_logFailures() {
    this.spinner = ora('步骤 4/8: 记录失败文件...').start();

    try {
      const failureLogger = new FailureLogger(this.projectPath);
      await failureLogger.initialize();

      // 记录所有失败的文件
      if (this.failedFiles && this.failedFiles.length > 0) {
        for (const failedFile of this.failedFiles) {
          await failureLogger.logFailure(
            failedFile.file,
            new Error(failedFile.error),
            { step: 'code-migration' }
          );
        }
      }

      await failureLogger.saveFailures();
      this.stats.stepResults.failureLogging = {
        failedCount: this.failedFiles.length
      };
      this.completeStep();

      if (this.failedFiles.length > 0) {
        this.spinner.warn(`步骤 4/8: 记录了 ${this.failedFiles.length} 个失败文件`);
      } else {
        this.spinner.succeed('步骤 4/8: 没有失败文件需要记录');
      }
    } catch (error) {
      this.spinner.fail('步骤 4/8: 失败文件记录失败');
      this.stats.errors.push({ step: 4, error: error.message });
      // 这个步骤失败不应该中断整个流程
      this.completeStep();
    }
  }

  /**
   * 步骤 5: AI 修复失败文件
   */
  async step5_aiRepair() {
    if (this.options.skipAIRepair) {
      this.skipStep('AI 修复');
      return;
    }

    this.spinner = ora('步骤 5/8: AI 修复失败文件...').start();

    try {
      const aiRepairer = new AIRepairer({ apiKey: this.options.aiApiKey });

      if (!aiRepairer.isEnabled()) {
        this.spinner.warn('步骤 5/8: AI 修复不可用（缺少 API Key）');
        this.completeStep();
        return;
      }

      if (this.failedFiles.length === 0) {
        this.spinner.succeed('步骤 5/8: 没有需要 AI 修复的文件');
        this.completeStep();
        return;
      }

      console.log(chalk.blue(`\n🤖 准备使用 AI 修复 ${this.failedFiles.length} 个失败文件...`));

      // 按错误类型分组显示
      const errorGroups = {};
      this.failedFiles.forEach(file => {
        if (!errorGroups[file.errorType]) {
          errorGroups[file.errorType] = [];
        }
        errorGroups[file.errorType].push(file);
      });

      if (this.options.verbose) {
        Object.entries(errorGroups).forEach(([errorType, files]) => {
          console.log(chalk.gray(`  ${errorType}: ${files.length} 个文件`));
        });
      }

      const result = await aiRepairer.repairFailedFiles(this.failedFiles, this.projectPath);
      this.stats.stepResults.aiRepair = result;
      this.completeStep();

      this.spinner.succeed('步骤 5/8: AI 修复完成');

      if (this.options.verbose && result) {
        console.log(chalk.gray(`  成功: ${result.success || 0}, 失败: ${result.failed || 0}`));
      }
    } catch (error) {
      this.spinner.fail('步骤 5/8: AI 修复失败');
      this.stats.errors.push({ step: 5, error: error.message });
      // AI 修复失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  AI 修复失败，继续后续步骤'));
      this.completeStep();
    }
  }

  /**
   * 步骤 6: Sass 语法迁移
   */
  async step6_sassMigration() {
    if (this.options.skipSassMigration) {
      this.skipStep('Sass 语法迁移');
      return;
    }

    this.spinner = ora('步骤 6/8: Sass 语法迁移...').start();

    try {
      const sassMigrator = new SassMigrator(this.projectPath, {
        verbose: this.options.verbose,
        backup: true,
        dryRun: false
      });

      // 检查是否有 Sass 文件需要迁移
      const sassFiles = await sassMigrator.findSassFiles();
      
      if (sassFiles.length === 0) {
        this.spinner.succeed('步骤 6/8: 未找到需要迁移的 Sass 文件');
        this.completeStep();
        return;
      }

      // 检查 sass-migrator 是否可用
      if (!await sassMigrator.isSassMigratorAvailable()) {
        this.spinner.warn('步骤 6/8: sass-migrator 不可用，跳过 Sass 迁移');
        console.log(chalk.yellow('   提示: 运行 "npm install -g sass-migrator" 安装工具'));
        this.completeStep();
        return;
      }

      // 暂停 spinner 以避免与 Sass 迁移输出混淆
      this.spinner.stop();
      console.log(chalk.blue('🎨 开始 Sass 语法迁移...'));

      const result = await sassMigrator.migrate();
      this.stats.stepResults.sassMigration = result;

      if (result.processedFiles > 0) {
        console.log(chalk.green(`✅ 成功迁移 ${result.processedFiles} 个 Sass 文件`));
      } else {
        console.log(chalk.gray('所有 Sass 文件无需迁移'));
      }

      this.completeStep();
    } catch (error) {
      this.spinner.fail('步骤 6/8: Sass 语法迁移失败');
      this.stats.errors.push({ step: 6, error: error.message });
      // Sass 迁移失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  Sass 迁移失败，继续后续步骤'));
      this.completeStep();
    }
  }

  /**
   * 步骤 6.5: 修复 Sass 变量引用
   */
  async step6_5_fixSassVariables() {
    if (this.options.skipSassMigration) {
      this.skipStep('修复 Sass 变量引用');
      return;
    }

    this.spinner = ora('步骤 6.5/8: 修复 Sass 变量引用...').start();

    try {
      const sassFixer = new SassVariableFixer(this.projectPath);
      await sassFixer.fix();
      
      this.spinner.succeed('步骤 6.5/8: Sass 变量引用修复完成');
      this.completeStep();
    } catch (error) {
      this.spinner.fail('步骤 6.5/8: Sass 变量引用修复失败');
      this.stats.errors.push({ step: 6.5, error: error.message });
      console.log(chalk.yellow('⚠️  Sass 变量修复失败，继续后续步骤'));
      this.completeStep();
    }
  }

  /**
   * 步骤 7: ESLint 自动修复
   */
  async step7_eslintFix() {
    if (this.options.skipESLint) {
      this.skipStep('ESLint 修复');
      return;
    }

    this.spinner = ora('步骤 7/8: ESLint 自动修复...').start();

    try {
      const eslintFixer = new ESLintFixer(this.projectPath);

      // 检查 ESLint 是否可用
      if (!await eslintFixer.isESLintAvailable()) {
        this.spinner.warn('步骤 7/8: ESLint 不可用，跳过此步骤');
        this.completeStep();
        return;
      }

      const result = await eslintFixer.fix();
      this.stats.stepResults.eslintFix = result;
      this.completeStep();

      this.spinner.succeed('步骤 7/8: ESLint 修复完成');

      if (this.options.verbose && result) {
        console.log(chalk.gray(`  修复文件: ${result.filesFixed || 0}, 修复错误: ${result.errorsFixed || 0}`));
      }
    } catch (error) {
      this.spinner.fail('步骤 7/8: ESLint 修复失败');
      this.stats.errors.push({ step: 7, error: error.message });
      // ESLint 修复失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  ESLint 修复失败，继续后续步骤'));
      this.completeStep();
    }
  }

  /**
   * 步骤 8: 构建项目并修复错误
   */
  async step8_buildAndFix() {
    if (this.options.skipBuild) {
      this.skipStep('构建修复');
      return;
    }

    this.spinner = ora('步骤 8/8: 构建项目并修复错误...').start();

    try {
      const aiRepairer = new AIRepairer({ apiKey: this.options.aiApiKey });
      const buildFixer = new BuildFixer(this.projectPath, {
        buildCommand: this.options.buildCommand,
        aiRepairer: aiRepairer.isEnabled() ? aiRepairer : null,
        verbose: this.options.verbose
      });

      // 暂停 spinner 以避免与构建输出混淆
      this.spinner.stop();
      console.log(chalk.blue('🏗️ 开始构建项目并修复错误...'));

      // 执行构建和修复
      const result = await buildFixer.buildAndFix();
      this.stats.stepResults.buildFix = result;

      // 构建完成后，总结结果
      if (result.success) {
        console.log(chalk.green('✅ 项目构建成功！'));
      } else {
        console.log(chalk.yellow('⚠️ 项目构建仍有问题，可能需要手动修复'));

        // 如果有错误摘要但不在verbose模式，显示错误摘要提示
        if (result.errorSummary && !this.options.verbose) {
          console.log(chalk.yellow('提示: 使用 --verbose 参数运行命令可查看详细错误信息'));
        }

        // 提供后续建议
        console.log(chalk.blue('\n💡 建议后续操作:'));
        console.log(chalk.gray('1. 检查 package.json 中的依赖是否正确'));
        console.log(chalk.gray('2. 确认项目配置文件（vue.config.js 等）已正确更新'));
        console.log(chalk.gray('3. 查看构建错误，手动修复剩余问题'));
      }

      // 显示构建统计
      console.log(chalk.gray(`构建尝试: ${result.attempts || 1}, 最终成功: ${result.success ? '是' : '否'}`));

      this.completeStep();
    } catch (error) {
      // 恢复 spinner 并显示失败
      this.spinner.fail('步骤 8/8: 构建修复失败');
      this.stats.errors.push({ step: 8, error: error.message });

      // 构建失败不应该中断整个流程
      console.log(chalk.yellow('⚠️ 构建过程发生错误，可能需要手动处理'));
      console.error(chalk.red('错误详情:'), error.message);

      this.completeStep();
    }
  }

  /**
   * 完成迁移
   */
  async completeMigration() {
    this.stats.endTime = Date.now();
    this.stats.duration = this.stats.endTime - this.stats.startTime;
    this.stats.success = this.stats.errors.length === 0;

    console.log('\n' + chalk.bold.green('🎉 Vue 2 到 Vue 3 迁移完成!'));
    this.printFinalStats();

    // 生成迁移报告
    await this.generateMigrationReport();

    // 显示后续建议
    this.printRecommendations();
  }

  /**
   * 处理迁移错误
   */
  async handleMigrationError(error) {
    this.stats.endTime = Date.now();
    this.stats.duration = this.stats.endTime - this.stats.startTime;
    this.stats.success = false;

    console.log('\n' + chalk.bold.red('❌ 迁移过程中发生错误'));
    console.log(chalk.red(error.message));

    if (this.options.verbose) {
      console.log(chalk.gray(error.stack));
    }

    await this.generateMigrationReport();
  }

  /**
   * 跳过步骤
   */
  skipStep(stepName) {
    console.log(chalk.gray(`⏭️  跳过: ${stepName}`));
    this.completeStep();
  }

  /**
   * 完成步骤
   */
  completeStep() {
    this.stats.completedSteps++;
  }

  /**
   * 打印最终统计
   */
  printFinalStats() {
    const duration = Math.round(this.stats.duration / 1000);

    console.log('\n' + chalk.bold('📊 迁移统计:'));
    console.log(`耗时: ${duration} 秒`);
    console.log(`完成步骤: ${this.stats.completedSteps}/${this.stats.totalSteps}`);
    console.log(`错误数量: ${this.stats.errors.length}`);

    if (this.stats.stepResults.codeMigration) {
      const cm = this.stats.stepResults.codeMigration;
      console.log(`代码文件: ${cm.success || 0} 成功, ${cm.failed || 0} 失败`);
    }

    if (this.stats.stepResults.buildFix?.success) {
      console.log(chalk.green('✅ 项目可以成功构建'));
    } else if (this.stats.stepResults.buildFix) {
      console.log(chalk.yellow('⚠️  项目构建可能仍有问题'));
    }
  }

  /**
   * 生成迁移报告
   */
  async generateMigrationReport() {
    const reportPath = path.join(this.projectPath, 'migration-report.json');

    const report = {
      timestamp: new Date().toISOString(),
      projectPath: this.projectPath,
      options: this.options,
      stats: this.stats,
      success: this.stats.success,
      duration: this.stats.duration,
      recommendations: this.generateRecommendations()
    };

    if (!this.options.dryRun) {
      await fs.writeJson(reportPath, report, { spaces: 2 });
    }

    console.log(chalk.blue(`📄 迁移报告已生成: ${reportPath}`));
  }

  /**
   * 生成建议
   */
  generateRecommendations() {
    const recommendations = [];

    if (this.stats.errors.length > 0) {
      recommendations.push('检查错误日志并手动修复剩余问题');
    }

    if (this.stats.stepResults.codeMigration?.failed > 0) {
      recommendations.push('手动检查迁移失败的文件');
    }

    if (!this.stats.stepResults.buildFix?.success) {
      recommendations.push('运行构建命令检查剩余的构建错误');
    }

    recommendations.push('运行 npm install 安装新依赖');
    recommendations.push('运行测试确保功能正常');
    recommendations.push('检查 UI 组件是否正确迁移到 Element Plus');
    recommendations.push('更新文档和部署配置');

    return recommendations;
  }

  /**
   * 打印建议
   */
  printRecommendations() {
    const recommendations = this.generateRecommendations();

    if (recommendations.length > 0) {
      console.log('\n' + chalk.bold('💡 后续建议:'));
      recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    }
  }

}

/**
 * 源到目标迁移器
 * 从源项目读取文件，转换后写入目标项目
 */
class SourceToTargetMigrator extends UnifiedVueMigrator {
  constructor(sourceProjectPath, targetProjectPath, options = {}) {
    super(targetProjectPath, options);
    this.sourceProjectPath = path.resolve(sourceProjectPath);
    this.targetProjectPath = path.resolve(targetProjectPath);
    
    // 初始化依赖映射器
    this.dependencyMapper = new DependencyMapper();
    this.dependencyCodeMigrator = new DependencyCodeMigrator(
      this.targetProjectPath, 
      this.dependencyMapper, 
      {
        aiApiKey: this.options.aiApiKey,
        dryRun: this.options.dryRun,
        verbose: this.options.verbose
      }
    );
  }

  async migrate() {
    try {
      console.log(chalk.bold.blue('\n🔄 Vue 2 到 Vue 3 源到目标迁移工具\n'));
      console.log(chalk.gray(`源项目: ${this.sourceProjectPath}`));
      console.log(chalk.gray(`目标项目: ${this.targetProjectPath}`));
      console.log(chalk.gray(`开始时间: ${new Date().toLocaleString()}`));
      console.log(chalk.gray(`模式: ${this.options.dryRun ? '预览模式' : '实际执行'}\n`));

      // 验证源项目
      await this.validateSourceProject();

      // 创建目标项目目录
      await this.createTargetProject();

      // 执行源到目标的增强8步迁移流程
      await this.step1_upgradePackageJsonWithMapping();
      await this.step2_checkDependencies();
      await this.step3_migrateCode();
      await this.step3_5_migrateDependencyCode(); // 新增步骤：迁移依赖相关代码
      await this.step4_logFailures();
      await this.step5_aiRepair();
      await this.step6_sassMigration(); // Sass 语法迁移
      await this.step6_5_fixSassVariables(); // 新增步骤：修复 Sass 变量引用
      await this.step7_eslintFix();
      await this.step8_buildAndFix();

      // 完成迁移
      await this.completeMigration();
    } catch (error) {
      await this.handleMigrationError(error);
      throw error;
    }
  }

  async validateSourceProject() {
    this.spinner = ora('验证源项目结构...').start();

    try {
      // 检查源项目目录是否存在
      if (!await fs.pathExists(this.sourceProjectPath)) {
        throw new Error(`源项目目录不存在: ${this.sourceProjectPath}`);
      }

      // 检查 package.json
      const packageJsonPath = path.join(this.sourceProjectPath, 'package.json');
      if (!await fs.pathExists(packageJsonPath)) {
        throw new Error('源项目中未找到 package.json 文件');
      }

      // 检查是否为 Vue 项目
      const packageJson = await fs.readJson(packageJsonPath);
      if (!packageJson.dependencies?.vue && !packageJson.devDependencies?.vue) {
        throw new Error('源项目不是一个 Vue 项目');
      }

      this.spinner.succeed('源项目验证通过');
    } catch (error) {
      this.spinner.fail('源项目验证失败');
      throw error;
    }
  }

  async createTargetProject() {
    this.spinner = ora('创建目标项目...').start();

    try {
      // 确保目标目录存在
      await fs.ensureDir(this.targetProjectPath);

      // 检查源项目的 src 目录是否存在
      const sourceSrcPath = path.join(this.sourceProjectPath, 'src');
      if (!await fs.pathExists(sourceSrcPath)) {
        throw new Error('源项目中未找到 src 目录');
      }

      // 只复制 src 目录到目标项目
      const targetSrcPath = path.join(this.targetProjectPath, 'src');
      console.log(chalk.gray(`   复制 src 目录: ${sourceSrcPath} → ${targetSrcPath}`));

      await fs.copy(sourceSrcPath, targetSrcPath, {
        overwrite: true,
        filter: (src) => {
          // 排除一些不必要的文件
          const fileName = path.basename(src);
          return !fileName.startsWith('.') &&
                 fileName !== 'node_modules' &&
                 !fileName.endsWith('.log');
        }
      });

      // 合并 package.json 而不是完全覆盖
      const sourcePackageJsonPath = path.join(this.sourceProjectPath, 'package.json');
      const targetPackageJsonPath = path.join(this.targetProjectPath, 'package.json');

      if (await fs.pathExists(sourcePackageJsonPath)) {
        console.log(chalk.gray('   合并 package.json...'));
        
        // 读取源项目的 package.json
        const sourcePackageJson = await fs.readJson(sourcePackageJsonPath);
        
        // 检查目标项目是否已有 package.json
        let targetPackageJson = {};
        if (await fs.pathExists(targetPackageJsonPath)) {
          targetPackageJson = await fs.readJson(targetPackageJsonPath);
        }
        
        // 保留目标项目的重要字段
        const preservedFields = ['type', 'packageManager', 'engines'];
        for (const field of preservedFields) {
          if (targetPackageJson[field] && !sourcePackageJson[field]) {
            sourcePackageJson[field] = targetPackageJson[field];
          }
        }
        
        // 保留目标项目中的 scripts 命令，特别是与现代构建工具相关的命令
        if (targetPackageJson.scripts) {
          sourcePackageJson.scripts = sourcePackageJson.scripts || {};
          
          // 定义需要保留的现代构建工具命令关键词
          const modernBuildTools = ['vite', 'vitest', 'tsc', 'unbuild', 'nuxt', 'astro', 'unocss'];
          
          // 遍历目标项目的 scripts
          for (const [scriptName, scriptCommand] of Object.entries(targetPackageJson.scripts)) {
            // 如果脚本命令包含现代构建工具关键词，或者源项目中没有该脚本，则保留目标项目的脚本
            if (modernBuildTools.some(tool => scriptCommand.includes(tool)) || !sourcePackageJson.scripts[scriptName]) {
              sourcePackageJson.scripts[scriptName] = scriptCommand;
            }
          }
        }
        
        // 合并依赖，保留目标项目中的现代依赖
        const modernDeps = [
          'vite', 'vitest', 'typescript', '@types', 'eslint', 'prettier',
          'unocss', 'unplugin', 'postcss', 'rollup', 'unbuild', '@vitejs'
        ];
        
        // Vue 3 特有的依赖前缀，这些依赖应该保留目标项目中的版本
        const vue3SpecificPrefixes = [
          '@vue/', '@vueuse/', '@vitejs/', 'vue-router', 'vuex', 'pinia',
          'vue-demi', 'vue-i18n', 'vue3-', 'vee-validate', 'element-plus'
        ];
        
        // 不迁移的 Vue 2 组件前缀
        const vue2SpecificPrefixes = [
          'vue-template-compiler', '@vue/cli-', 'vue2-', 'vuetify'
        ];
        
        // 处理依赖的函数
        const processDependencies = (sourceDeps, targetDeps) => {
          const result = { ...sourceDeps } || {};
          
          // 先删除不要迁移的 Vue 2 组件
          Object.keys(result).forEach(name => {
            if (vue2SpecificPrefixes.some(prefix => name.startsWith(prefix))) {
              delete result[name];
            }
          });
          
          // 然后处理目标项目中的依赖
          if (targetDeps) {
            for (const [name, version] of Object.entries(targetDeps)) {
              // 如果是现代工具依赖或 Vue 3 特有依赖，保留目标项目的版本
              if (modernDeps.some(dep => name.startsWith(dep)) || 
                  vue3SpecificPrefixes.some(prefix => name.startsWith(prefix))) {
                result[name] = version;
              }
            }
          }
          
          return result;
        };
        
        // 处理主依赖
        sourcePackageJson.dependencies = processDependencies(
          sourcePackageJson.dependencies, 
          targetPackageJson.dependencies
        );
        
        // 处理开发依赖
        sourcePackageJson.devDependencies = processDependencies(
          sourcePackageJson.devDependencies, 
          targetPackageJson.devDependencies
        );
        
        // 写入合并后的 package.json
        await fs.writeJson(targetPackageJsonPath, sourcePackageJson, { spaces: 2 });
      } else {
        throw new Error('源项目中未找到 package.json 文件');
      }

      this.spinner.succeed('目标项目创建完成（仅复制 src 目录）');
    } catch (error) {
      this.spinner.fail('目标项目创建失败');
      throw error;
    }
  }

  // 重写步骤1，集成依赖映射功能
  async step1_upgradePackageJsonWithMapping() {
    this.spinner = ora('步骤 1/7: 升级 package.json 依赖（含依赖映射）...').start();

    try {
      const targetPackageJsonPath = path.join(this.targetProjectPath, 'package.json');

      // 1. 首先使用 PackageUpgrader 升级基础 Vue 3 依赖
      console.log(chalk.gray('\n   📦 升级基础 Vue 3 依赖...'));
      const upgrader = new PackageUpgrader(this.targetProjectPath, {
        dryRun: this.options.dryRun,
        verbose: this.options.verbose,
        migrationMode: true,  // 这是迁移模式
        preserveVue3Dependencies: true  // 保留目标项目的 Vue 3 依赖
      });

      const upgradeResult = await upgrader.upgrade();

      // 2. 然后处理依赖映射（在 PackageUpgrader 之后执行）
      console.log(chalk.gray('\n   🔄 处理依赖映射...'));
      const mappingResult = await this.dependencyMapper.updatePackageJsonDependencies(
        targetPackageJsonPath, 
        this.options.dryRun
      );

      if (mappingResult.updated > 0) {
        console.log(chalk.green(`   ✅ 映射了 ${mappingResult.updated} 个依赖:`));
        mappingResult.dependencies.forEach(dep => {
          console.log(chalk.gray(`      ${dep.source} → ${dep.target}`));
        });
      } else {
        console.log(chalk.gray('   📦 没有找到需要映射的依赖'));
      }
      
      // 合并结果
      this.stats.stepResults.packageUpgrade = {
        ...upgradeResult,
        dependencyMapping: mappingResult
      };

      this.spinner.succeed('步骤 1/7: package.json 依赖升级完成（含依赖映射）');
      this.completeStep();
    } catch (error) {
      this.spinner.fail('步骤 1/7: package.json 依赖升级失败');
      this.stats.errors.push(error.message);
      throw error;
    }
  }

  // 新增步骤：迁移依赖相关代码
  async step3_5_migrateDependencyCode() {
    this.spinner = ora('步骤 3.5/7: 迁移依赖相关代码...').start();

    try {
      console.log(chalk.gray('\n   🤖 使用 AI 迁移依赖相关代码...'));
      
      const result = await this.dependencyCodeMigrator.migrateAllDependencyCode();
      
      this.stats.stepResults.dependencyCodeMigration = result;

      if (result.totalFiles > 0) {
        console.log(chalk.green(`   ✅ 处理了 ${result.totalFiles} 个文件，成功迁移 ${result.migratedFiles} 个`));
        
        if (result.errors.length > 0) {
          console.log(chalk.yellow(`   ⚠️  ${result.errors.length} 个文件迁移失败`));
          if (this.options.verbose) {
            result.errors.forEach(error => {
              console.log(chalk.gray(`      ${error.file}: ${error.error}`));
            });
          }
        }
      } else {
        console.log(chalk.gray('   📦 没有找到需要迁移的依赖代码'));
      }

      this.spinner.succeed('步骤 3.5/7: 依赖相关代码迁移完成');
      this.completeStep();
    } catch (error) {
      this.spinner.fail('步骤 3.5/7: 依赖相关代码迁移失败');
      this.stats.errors.push(error.message);
      // 不抛出错误，因为这不是关键步骤
      console.log(chalk.yellow('⚠️  依赖代码迁移失败，但迁移流程将继续'));
    }
  }

  /**
   * 步骤 7: ESLint 自动修复 (委托给父类的 step7_eslintFix)
   */
  async step7_eslintFix() {
    // 委托给父类的 step7_eslintFix 方法
    return await super.step7_eslintFix();
  }

  /**
   * 步骤 8: 构建项目并修复错误 (委托给父类的 step8_buildAndFix)
   */
  async step8_buildAndFix() {
    // 委托给父类的 step8_buildAndFix 方法
    return await super.step8_buildAndFix();
  }

}

// CLI 接口
const program = new Command();

program
  .name('vue-migrator')
  .description('Vue 2 到 Vue 3 统一迁移工具')
  .version('1.0.0');

// 自动迁移命令（推荐）
program
  .command('auto [project-path] [destination-path]')
  .description('🚀 自动检测项目类型并执行最佳迁移策略（推荐）')
  .option('--force', '强制迁移（即使检测到 Vue 3 项目）')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--skip-backup', '跳过项目备份')
  .option('--ai-key <key>', 'AI API Key (支持 OpenAI/DeepSeek/GLM)')
  .option('--verbose', '显示详细信息')
  .action(async (projectPath, destinationPath, options) => {
    try {
      const sourcePath = path.resolve(projectPath || process.cwd());

      // 如果提供了目标路径，则为新目录迁移模式
      if (destinationPath) {
        const destPath = path.resolve(destinationPath);

        console.log(chalk.bold.blue('\n🚀 Vue 自动化迁移工具 - 新目录模式\n'));
        console.log(chalk.gray(`源项目: ${sourcePath}`));
        console.log(chalk.gray(`目标项目: ${destPath}`));

        // 确保目标目录存在
        await fs.ensureDir(destPath);

        // 复制项目到新目录
        console.log(chalk.blue('📁 复制项目文件...'));
        await fs.copy(sourcePath, destPath, {
          overwrite: true,
          filter: (src) => {
            // 排除一些不必要的文件
            const fileName = path.basename(src);
            return !fileName.startsWith('.') &&
                   fileName !== 'node_modules' &&
                   !fileName.endsWith('.log');
          }
        });

        const autoMigrator = new AutoMigrator(sourcePath, {
          ...options,
          destinationPath: destPath,
          newDirectoryMode: true
        });

        await autoMigrator.migrate();

      } else {
        // 原地迁移模式（保持原有行为）
        console.log(chalk.bold.blue('\n🚀 Vue 自动化迁移工具 - 原地迁移模式\n'));
        console.log(chalk.gray(`项目路径: ${sourcePath}`));

        const autoMigrator = new AutoMigrator(sourcePath, options);
        await autoMigrator.migrate();
      }

    } catch (error) {
      console.error(chalk.red('\n❌ 自动迁移失败:'), error.message);
      if (process.env.DEBUG) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  });

// 新旧工程迁移命令（默认命令，包含完整7步迁移）
program
  .command('migrate-to <old-project> <new-project>')
  .description('🔄 从旧 Vue 2 工程迁移到新 Vue 3 工程（完整7步迁移流程）')
  .option('--skip-dependency-check', '跳过依赖兼容性检查')
  .option('--skip-ai', '跳过 AI 修复步骤')
  .option('--eslint', '启用 ESLint 自动修复（默认禁用）')
  .option('--skip-build', '跳过构建和构建错误修复')
  .option('--ai-key <key>', 'AI API Key (支持 DeepSeek/GLM/OpenAI)')
  .option('--build-command <cmd>', '构建命令', 'npm run build')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--verbose', '显示详细信息')
  .action(async (oldProject, newProject, options) => {
    try {
      const oldProjectPath = path.resolve(oldProject);
      const newProjectPath = path.resolve(newProject);

      // 验证源项目路径
      if (!await fs.pathExists(oldProjectPath)) {
        throw new Error(`源项目路径不存在: ${oldProjectPath}`);
      }

      // 使用 SourceToTargetMigrator 执行真正的源到目标迁移
      const migrator = new SourceToTargetMigrator(oldProjectPath, newProjectPath, {
        skipDependencyCheck: options.skipDependencyCheck,
        skipAIRepair: options.skipAi,
        skipESLint: !options.eslint,
        skipBuild: options.skipBuild,
        aiApiKey: options.aiKey,
        buildCommand: options.buildCommand,
        dryRun: options.dryRun,
        verbose: options.verbose
      });

      await migrator.migrate();

      // 生成迁移报告
      console.log(chalk.blue('\n📄 生成迁移报告...'));
      await generateMigrationReport(oldProjectPath, newProjectPath);

      console.log(chalk.bold.green('\n🎉 源到目标迁移完成！'));
      console.log(chalk.yellow('\n💡 后续建议:'));
      console.log('1. 检查迁移后的代码是否正常工作');
      console.log('2. 运行测试确保功能正常');

    } catch (error) {
      console.error(chalk.red('\n❌ 迁移失败:'), error.message);
      if (process.env.DEBUG) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  });

// 生成迁移报告的辅助函数
async function generateMigrationReport(oldProjectPath, newProjectPath) {
  try {
    const MigrationDocGenerator = require('../src/migrator/migrationDocGenerator');

    console.log(chalk.gray('正在生成迁移报告...'));

    // 使用旧项目的 package.json 生成分析结果
    const docGenerator = new MigrationDocGenerator(oldProjectPath, null, {
      outputPath: path.join(newProjectPath, 'migration-report.md'),
      includeUsageDetails: false,
      language: 'zh-CN'
    });

    const result = await docGenerator.generateMigrationGuide();

    if (result.success) {
      console.log(chalk.green(`✅ 迁移报告已生成: ${result.outputPath}`));
    } else {
      console.log(chalk.yellow('⚠️  迁移报告生成失败'));
    }
  } catch (error) {
    console.log(chalk.yellow('⚠️  迁移报告生成失败:'), error.message);
  }
}

// 显示步骤说明
program
  .command('steps')
  .description('显示迁移步骤说明')
  .action(() => {
    console.log(chalk.bold.blue('\n🚀 Vue 2 到 Vue 3 迁移步骤说明\n'));

    console.log(chalk.bold.green('🎯 自动迁移模式（强烈推荐）:'));
    console.log(chalk.green('vue-migrator auto [project-path]'));
    console.log(chalk.gray('- 🔍 自动检测项目类型（Vue Element Admin、Vue 2、Vue 3）'));
    console.log(chalk.gray('- ⚙️  自动应用最佳配置（无需手动输入参数）'));
    console.log(chalk.gray('- 🤖 智能选择 AI 提供商（DeepSeek > GLM > OpenAI）'));
    console.log(chalk.gray('- 📦 自动备份项目文件'));
    console.log(chalk.gray('- 🚀 一键完成完整迁移流程\n'));

    console.log(chalk.bold.green('🔄 完整迁移模式（默认推荐）:'));
    console.log(chalk.green('vue-migrator migrate-to <old-project> <new-project>'));
    console.log(chalk.gray('- 📁 自动创建新项目目录并复制文件'));
    console.log(chalk.gray('- 🚀 执行完整的8步迁移流程'));
    console.log(chalk.gray('- 📄 生成详细的迁移报告\n'));

    console.log(chalk.bold('完整迁移的8个步骤:'));
    const steps = [
      '1. 升级 package.json 依赖 - 将 Vue 相关依赖升级到 Vue 3 版本',
      '2. 检查依赖兼容性 - 检查第三方依赖是否支持 Vue 3',
      '3. 批量迁移代码文件 - 使用 Gogocode 转换 .vue 和 .js 文件',
      '4. 记录失败文件 - 记录转换失败的文件供后续处理',
      '5. AI 修复失败文件 - 使用 AI 自动修复转换失败的文件',
      '6. Sass 语法迁移 - 将 @import 语句迁移到 @use 语法',
      '7. ESLint 自动修复 - 运行 ESLint 修复格式和语法问题',
      '8. 构建项目并修复错误 - 尝试构建项目并使用 AI 修复构建错误'
    ];

    steps.forEach((step, index) => {
      console.log(chalk.green(`${index + 1}. ${step.split(' - ')[0]}`));
      console.log(chalk.gray(`   ${step.split(' - ')[1]}\n`));
    });

    console.log(chalk.yellow('💡 使用建议:'));
    console.log(chalk.bold.green('🎯 自动模式（推荐）:'));
    console.log('- vue-migrator auto                    # 自动迁移当前目录项目');
    console.log('- vue-migrator auto /path/to/project   # 自动迁移指定项目');
    console.log('- vue-migrator auto --dry-run          # 预览模式，不实际修改');
    console.log('- vue-migrator auto --ai-key <key>     # 指定 AI API Key');
    console.log('\n🔄 完整迁移模式（默认）:');
    console.log('- vue-migrator migrate-to <old> <new>  # 完整7步迁移到新目录');
    console.log('- vue-migrator migrate-to <old> <new> --dry-run  # 预览模式');
    console.log('- vue-migrator migrate-to <old> <new> --ai-key <key>  # 指定 AI Key');
  });

// 全局错误处理
process.on('uncaughtException', (error) => {
  console.error(chalk.red('❌ 未捕获的异常:'), error.message);
  if (process.env.DEBUG) {
    console.error(error.stack);
  }
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('❌ 未处理的 Promise 拒绝:'), reason);
  if (process.env.DEBUG) {
    console.error(promise);
  }
  process.exit(1);
});

// 解析命令行参数
program.parse();

// 如果没有提供命令，显示帮助
if (!process.argv.slice(2).length) {
  program.outputHelp();
}

module.exports = UnifiedVueMigrator;
